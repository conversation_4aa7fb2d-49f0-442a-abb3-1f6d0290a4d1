package team.aikero.murmuration.ksp

import com.google.devtools.ksp.processing.*
import com.google.devtools.ksp.symbol.KSAnnotated
import com.google.devtools.ksp.symbol.KSClassDeclaration

/**
 * 节点控制器符号处理器
 *
 * 扫描使用 @NodeIdentifier 注解的 Node 类，自动生成对应的 REST Controller
 *
 * <AUTHOR>
 */
class TaskNodeControllerSymbolProcessor(
    val codeGenerator: CodeGenerator,
) : SymbolProcessor {
    var invoked = false

    override fun process(resolver: Resolver): List<KSAnnotated> {
        if (invoked) {
            return emptyList()
        }

        val nodeIdentifierFqName = "team.aikero.murmuration.core.annotations.NodeIdentifier"
        val controllerPackage = "team.aikero.murmuration.service.controller.node"

        resolver.getSymbolsWithAnnotation(nodeIdentifierFqName)
            .filterIsInstance<KSClassDeclaration>()
            .forEach { classDecl ->
                // 1. 获取父类 TaskNode<Input, Parameter, Output>
                val superType = classDecl.superTypes
                    .map { it.resolve() }
                    .find { it.declaration.qualifiedName?.asString() == "team.aikero.murmuration.core.workflow.node.builtin.TaskNode" }
                if (superType == null) return@forEach

                val typeArgs = superType.arguments
                val inputType = typeArgs.getOrNull(0)?.type?.resolve()
                val parameterType = typeArgs.getOrNull(1)?.type?.resolve()
                val outputType = typeArgs.getOrNull(2)?.type?.resolve()
                val inputTypeName = inputType?.declaration?.qualifiedName?.asString() ?: return@forEach
                val parameterTypeName = parameterType?.declaration?.qualifiedName?.asString() ?: return@forEach
                val outputTypeName = outputType?.declaration?.qualifiedName?.asString() ?: return@forEach

                // 获取注解字段及其注释
                val nodeIdentifierAnnotation = classDecl.annotations
                    .find { it.annotationType.resolve().declaration.qualifiedName?.asString() == nodeIdentifierFqName }

                var nodeName = ""
                var supplierInfo = ""
                var abilityInfo = ""

                nodeIdentifierAnnotation?.arguments?.forEach { arg ->
                    when (arg.name?.asString()) {
                        "name" -> {
                            nodeName = arg.value.toString()
                        }

                        "supplier" -> {
                            supplierInfo = arg.value.toString().replace("team.aikero.murmuration.core.", "")
                        }

                        "ability" -> {
                            abilityInfo =  arg.value.toString().replace("team.aikero.murmuration.core.", "")
                        }
                    }
                }

                // 获取类注释（只取第一行描述）
                val classComment = classDecl.docString?.trim()?.let { docString ->
                    // 提取第一行非空的描述内容，忽略 @author、@since 等标签
                    docString.lines()
                        .map { it.trim() }.firstOrNull { it.isNotEmpty() && !it.startsWith("@") } ?: ""
                } ?: ""

                // 2. 生成 Controller 类名和路径
                val nodeNameClean = classDecl.simpleName.asString() // 如 PushPodStyleNode
                val baseName = nodeNameClean.removeSuffix("TaskNode")
                val controllerName = "${baseName}NodeController"
                val requestMapping = "/inner/node/" + baseName.toKebabCase()

                // 3. 生成 Controller 代码
                val file = codeGenerator.createNewFile(
                    Dependencies(false, classDecl.containingFile!!),
                    controllerPackage,
                    controllerName
                )
                file.bufferedWriter().use { writer ->
                    writer.write(
                        """
                        package $controllerPackage

                        import com.fasterxml.jackson.databind.ObjectMapper
                        import org.babyfish.jimmer.sql.kt.KSqlClient
                        import org.springframework.transaction.annotation.Transactional
                        import org.springframework.web.bind.annotation.*
                        import team.aikero.blade.core.protocol.DataResponse
                        import team.aikero.blade.core.protocol.ok
                        import ${classDecl.qualifiedName?.asString()}
                        import $inputTypeName
                        import $parameterTypeName
                        import $outputTypeName
                        import team.aikero.murmuration.core.Supplier
                        import team.aikero.murmuration.core.Ability

                        /**
                         * [INNER] 节点信息
                         *
                         * > 主要用于提供前端可读的节点参数信息
                         *
                         * <AUTHOR>
                         */
                        @RestController
                        @RequestMapping("$requestMapping")
                        class $controllerName(
                            val sql: KSqlClient,
                            val objectMapper: ObjectMapper,
                            val node: $nodeNameClean,
                        ) {

                            /**
                             * 节点定义 - ${classComment.ifEmpty { (nodeName.ifEmpty { baseName }) }}  
                             * 
                             * $nodeNameClean   
                             * 
                             * @see $supplierInfo   
                             * @see $abilityInfo   
                             * 
                             * @param request 节点元数据信息  
                             * @return 节点输出信息  
                             */
                            @PostMapping("/create-metadata")
                            @Transactional(rollbackFor = [Exception::class])
                            fun nodeDefinition(@RequestBody request: NodeMetadataRequest): DataResponse<${outputType.declaration.simpleName.asString()}> {
                                // 什么都不做
                                return ok()
                            }
                            
                            /**
                             * 节点元数据请求
                             *
                             * @param supplier 供应商
                             * @param ability 能力
                             * @param input 输入参数
                             * @param parameter 节点参数
                             */
                            data class NodeMetadataRequest(
                                /**
                                 * 供应商
                                 */
                                val supplier: Supplier,
                                /**
                                 * 能力
                                 */
                                val ability: Ability,
                                /**
                                 * 输入参数
                                 */
                                val input: ${inputType.declaration.simpleName.asString()},
                                /**
                                 * 节点参数
                                 */
                                val parameter: ${parameterType.declaration.simpleName.asString()},
                            )
                        }
                        
                        """.trimIndent()
                    )
                }
            }

        invoked = true
        return emptyList()
    }

    internal fun String.toKebabCase(): String {
        // 驼峰转为 kebab-case
        return this
            .replace(Regex("([a-z])([A-Z])"), "$1-$2")
            .replace(Regex("([A-Z])([A-Z][a-z])"), "$1-$2")
            .lowercase()
    }
}

class NodeControllerSymbolProcessorProvider : SymbolProcessorProvider {
    override fun create(environment: SymbolProcessorEnvironment): SymbolProcessor {
        return TaskNodeControllerSymbolProcessor(environment.codeGenerator)
    }
}
