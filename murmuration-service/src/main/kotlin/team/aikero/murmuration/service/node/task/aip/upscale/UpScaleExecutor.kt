package team.aikero.murmuration.service.node.task.aip.upscale

import team.aikero.murmuration.service.node.task.aip.*


@AipExecutorIdentifier(AipAbility.UPSCALE)
class UpScaleExecutor(client: AipClient): AipExecutor<UpScaleInput, UpScaleOutput>(client) {

    override fun validateTaskOutput(output: UpScaleOutput) {
        if (output.resImgs.isEmpty()) {
            throw InvalidTaskOutputException("resImgs 数组为空")
        }
    }
}

data class UpScaleInput(
    val inputImage: String,
    val scalefactor: Int,
)

data class UpScaleOutput(
    val resImgs: List<String>,
)
