package team.aikero.murmuration.service.node.task.aip.upscale

import team.aikero.murmuration.common.enums.task.Resolution
import team.aikero.murmuration.common.req.task.UpScaleRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.builtin.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.service.node.shared.ImageListInput
import team.aikero.murmuration.service.node.shared.ImageListOutput
import team.aikero.murmuration.service.node.shared.SimpleImage

/**
 * 超分任务节点
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@NodeIdentifier(name = "超分", supplier = Supplier.REALESRGAN, ability = Ability.UPSCALE)
class UpScaleTaskNode : TaskNode<ImageListInput, UpScaleNodeParameter, ImageListOutput>() {

    override fun createTask(context: WorkflowNodeContext, input: ImageListInput, parameter: UpScaleNodeParameter) {
        // 循环创建超分任务
        for (image in input.images) {
            taskManager.createTask(
                nodeInstanceId = context.node.id,
                supplier = Supplier.REALESRGAN,
                ability = Ability.UPSCALE,
                request = UpScaleRequest(
                    imageUrl = image.getUrl(),
                    targetResolution = parameter.targetResolution,
                ),
            )
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): ImageListOutput {
        val images = taskResults.map {
            SimpleImage(it.url)
        }
        return ImageListOutput(images)
    }
}

data class UpScaleNodeParameter(
    /**
     * 目标分辨率
     */
    val targetResolution: Resolution = Resolution.R_2K,
) : Parameter
