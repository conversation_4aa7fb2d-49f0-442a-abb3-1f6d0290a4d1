package team.aikero.murmuration.service.node.task.youchuan

import org.springframework.stereotype.Component
import org.springframework.web.client.HttpClientErrorException
import team.aikero.murmuration.core.util.requiredProperty
import team.aikero.murmuration.core.workflow.task.TaskCreationRetryableException
import team.aikero.murmuration.core.workflow.task.TaskResult

/**
 * 悠船客户端
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Component
@Suppress("SpringJavaInjectionPointsAutowiringInspection")
class YouChuanClient(val httpClient: YouChuanHttpClient) {

    /**
     * 文生图
     *
     * @param prompt 提示词
     * @return 任务ID
     */
    fun diffusion(prompt: String): String {
        check(prompt.length in 1..8192) { "提示词长度必须在1-8192之间" }
        val (app, secret) = getCertificate()

        try {
            val jobInfo = httpClient.diffusion(
                app = app,
                secret = secret,
                request = DiffusionRequest(prompt),
            )

            // 处理下游限流异常
            if (jobInfo.status == JobStatus.FAIL && jobInfo.comment == "您已达到同时任务数上限") {
                throw TaskCreationRetryableException(jobInfo)
            }

            return jobInfo.id
        }
        // 处理下游限流异常
        // {"code":429,"message":"已达当前套餐最大并发","reason":"Max_Concurrent_Limited"}
        catch (ex: HttpClientErrorException.TooManyRequests) {
            val responseBody: String? = ex.responseBodyAsString
            if (responseBody != null && responseBody.contains("\"Max_Concurrent_Limited\"")) {
                throw TaskCreationRetryableException(responseBody)
            }
            throw ex
        }
    }

    /**
     * 查询任务结果
     *
     * @param jobId 任务ID
     * @return 任务结果
     */
    fun getJobResult(jobId: String): TaskResult<List<String>> {
        val (app, secret) = getCertificate()
        val jobInfo = httpClient.getJobInfo(
            app = app,
            secret = secret,
            jobId = jobId,
        )
        return when (jobInfo.status) {
            JobStatus.CREATED -> TaskResult.Running
            JobStatus.EXECUTION -> TaskResult.Running
            JobStatus.SUCCESS -> TaskResult.Completed(jobInfo.urls)
            JobStatus.FAIL -> TaskResult.Failed(jobInfo.comment)
        }
    }

    /**
     * 创建Moodboard
     * @param images 图片URL数组, 数量[1,30]
     * @param tag 用于标识（非必需)，传入风格名称
     */
    fun createMoodboard(images: List<String>, tag: String?): String {
        val (app, secret) = getCertificate()
        val moodboardInfo = httpClient.createMoodboard(
            app = app,
            secret = secret,
            request = MoodboardRequest(images, tag),
        )
        return moodboardInfo.id
    }

    /**
     * 更新Moodboard
     * @param id 创建后返回的id
     * @param images 图片URL数组, 数量[1,30]
     */
    fun updateMoodboard(id: String, images: List<String>): String {
        val (app, secret) = getCertificate()
        val moodboardInfo = httpClient.updateMoodboard(
            app = app,
            secret = secret,
            id = id,
            request = MoodboardRequest(images),
        )
        return moodboardInfo.id
    }

    /**
     * Moodboard分页列表
     */
    fun pageMoodboard(pageNo: Int = 1, pageSize: Int =10): MoodboardPageResponse {
        val (app, secret) = getCertificate()
        return httpClient.pageMoodboard(
            app = app,
            secret = secret,
            request = MoodboardPageRequest(pageNo, pageSize)
        )
    }

    private fun getCertificate(): Certificate {
        val app = requiredProperty<String>("youchuan.app")
        val secret = requiredProperty<String>("youchuan.secret")
        return Certificate(app, secret)
    }
}

data class Certificate(
    val app: String,
    val secret: String
)

