package team.aikero.murmuration.service.node.task.aigc_image

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.GeneratedValue
import org.babyfish.jimmer.sql.Id
import team.aikero.blade.data.jimmer.SnowflakeIdGenerator
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.Creator
import team.aikero.blade.data.jimmer.entity.TenantId

/**
 * 图案库
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Entity
interface GraphicStorehouse: Creator, CreatedTime, TenantId {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generatorType = SnowflakeIdGenerator::class)
    val imageId: Long

    /**
     * 来源类型
     */
    val originType: String

    /**
     * 图片地址
     */
    val imageUrl: String

    /**
     * 图片类型
     */
    val imageType: String

    /**
     * 图片格式
     */
    val imageFormat: String
}
