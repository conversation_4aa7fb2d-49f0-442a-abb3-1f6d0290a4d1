package team.aikero.murmuration.service.node.task.lazada

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import org.apache.commons.codec.binary.Hex
import org.apache.commons.codec.digest.HmacAlgorithms
import org.apache.commons.codec.digest.HmacUtils
import org.springframework.stereotype.Component
import team.aikero.murmuration.core.util.requiredProperty
import team.aikero.murmuration.core.workflow.task.TaskCreationRetryableException
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.core.workflow.task.TaskResult.InternalErrorType

/**
 * Lazada客户端
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Component
class LazadaClient(
    val objectMapper: ObjectMapper,
    val lazadaHttpClient: LazadaHttpClient
) {

    /**
     * AI换脸
     *
     * @param request 请求
     * @return 任务ID
     */
    fun changeFace(request: ChangeFaceRequest): String {
        val commonParams = createCommonParamsWithSign("/content/ai/changeFace", request)
        val response = lazadaHttpClient.changeFace(commonParams, request)
        return extractTaskId(response)
    }

    /**
     * AI换背景
     *
     * @param request 请求
     * @return 任务ID
     */
    fun changeBackground(request: ChangeBackgroundRequest): String {
        val commonParams = createCommonParamsWithSign("/content/ai/changeProductBackground", request)
        val response = lazadaHttpClient.changeBackground(commonParams, request)
        return extractTaskId(response)
    }

    /**
     * 虚拟换衣
     *
     * @param request 请求
     * @return 任务ID
     */
    fun tryOnCloth(request: TryOnClothRequest): String {
        val commonParams = createCommonParamsWithSign("/content/ai/tryOnCloth", request)
        val response = lazadaHttpClient.tryOnCloth(commonParams, request)
        return extractTaskId(response)
    }

    /**
     * 查询任务状态
     */
    fun getTaskStatus(taskId: String): TaskResult<List<String>> {
        val commonParams = createCommonParamsWithSign("/content/ai/getTaskStatus", mapOf("task_id" to taskId))
        val response = lazadaHttpClient.getTaskStatus(commonParams, taskId)
        val result = requireSuccessful(response)

        if (!result.success) {
            throw IllegalStateException("HTTP 响应体 result.success 为 false: resultCode=${result.resultCode}, resultMessage=${result.resultMessage}")
        }
        if (result.status == null) {
            throw NullPointerException("HTTP 响应体 result.status 为空")
        }

        return when (result.status) {
            GetTaskResult.Status.WAITING, GetTaskResult.Status.RUNNING -> TaskResult.Running
            GetTaskResult.Status.CANCELED -> TaskResult.Failed(InternalErrorType.TASK_CANCELED)
            GetTaskResult.Status.FAILED -> TaskResult.Failed(result.failMessage)
            GetTaskResult.Status.SUCCESS -> TaskResult.Completed(result.data ?: throw NullPointerException("HTTP 响应体 result.data 为空"))
        }
    }

    private fun createCommonParamsWithSign(uri: String, requestData: Any): Map<String, String> {
        val commonParams = getCommonParams()
        val requestParams = objectMapper.convertValue<Map<String, String?>>(requestData)
            .filterValues { it != null }
            .mapValues { it.value!! }

        val allParams = commonParams + requestParams
        val signature = sign(uri, allParams)

        return commonParams + ("sign" to signature)
    }

    private fun extractTaskId(response: Response<CreateTaskResult>): String {
        val result = requireSuccessful(response)
        if (!result.success) {
            throw IllegalStateException("HTTP 响应体 result.success 为 false: resultCode=${result.resultCode}, resultMessage=${result.resultMessage}")
        }
        return result.taskId ?: throw NullPointerException("HTTP 响应体 result.taskId 为空")
    }

    private fun <T> requireSuccessful(response: Response<T>): T {
        if (response.code != "0") {

            // 处理下游限流异常
            if (response.code == "ApiCallLimit") {
                throw TaskCreationRetryableException(response)
            }

            throw IllegalStateException("HTTP 响应体 code 不为 0: code=${response.code}, message=${response.message}, requestId=${response.requestId}")
        }

        if (response.result == null) {
            throw NullPointerException("HTTP 响应体 result 为空")
        }

        return response.result
    }

    private fun sign(uri: String, allParams: Map<String, String>): String {
        val sortedKeys = allParams.keys.sorted()
        val sb = StringBuilder(uri)
        for (key in sortedKeys) {
            val value = allParams[key]
            if (!value.isNullOrEmpty()) {
                sb.append(key).append(value)
            }
        }
        val appSecret = requiredProperty<String>("lazada.appSecret")
        val hmac = HmacUtils(HmacAlgorithms.HMAC_SHA_256, appSecret)
        val bytes = hmac.hmac(sb.toString())
        return Hex.encodeHexString(bytes, false)
    }

    private fun getCommonParams(): Map<String, String> {
        val appKey = requiredProperty<String>("lazada.appKey")
        return mapOf(
            "app_key" to appKey,
            "timestamp" to "${System.currentTimeMillis()}",
            "sign_method" to "sha256",
            "partner_id" to "lazop-sdk-java-20181207"
        )
    }
}
