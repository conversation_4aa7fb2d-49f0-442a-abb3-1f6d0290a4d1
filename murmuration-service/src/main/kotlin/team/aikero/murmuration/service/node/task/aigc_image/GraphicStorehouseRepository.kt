package team.aikero.murmuration.service.node.task.aigc_image

import cn.hutool.core.io.FileUtil
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.stereotype.Component

/**
 * 图案库仓库
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Component
class GraphicStorehouseRepository(val sql: KSqlClient) {

    fun insert(
        imageUrl: String,
        tenantId: Long,
    ): Long {
        val entity = toEntity(imageUrl, tenantId)
        val image = sql.insert(entity).modifiedEntity
        return image.imageId
    }

    fun insertBatch(
        imageUrls: Iterable<String>,
        tenantId: Long,
    ): List<GraphicStorehouse> {
        val entities = imageUrls.map { toEntity(it, tenantId) }
        return sql.insertEntities(entities).items.map { it.modifiedEntity }
    }

    private fun toEntity(imageUrl: String, tenantId: Long) = GraphicStorehouse {
        this.originType = "9000"
        this.imageUrl = imageUrl
        this.imageType = "9000"
        this.imageFormat = FileUtil.extName(imageUrl)
        this.tenantId = tenantId
    }
}
