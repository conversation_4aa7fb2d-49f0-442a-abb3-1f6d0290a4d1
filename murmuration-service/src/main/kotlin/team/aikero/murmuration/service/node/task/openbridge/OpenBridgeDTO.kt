package team.aikero.murmuration.service.node.task.openbridge

import com.fasterxml.jackson.annotation.JsonValue

/**
 * 图片检测请求
 */
data class ImageModerationRequest(
    /**
     * 请求ID（UUID）
     * <p>
     * 方便溯源排查问题
     */
    val requestId: String,

    /**
     * 图片链接
     */
    val image: String,

    /**
     * 指定检测项
     * <p>
     * 不能同时指定海外跟国内服检测服务，要么全部指定国内服务，要么全部指定海外服务
     * <p>
     * baselineCheck：通用基线检测
     * baselineCheck_pro：通用基线检测_专业版
     * baselineCheck_cb：通用基线检测_出海版
     * tonalityImprove：内容治理检测
     * tonalityImprove_cb：内容治理检测_出海版
     * aigcCheck：AIGC图片风险
     * aigcCheck_cb：AIGC图片风险检测_出海版
     * profilePhotoCheck：头像图片检测
     * postImageCheck：帖子评论图片检测
     * advertisingCheck：营销素材检测
     * liveStreamCheck：视频\直播截图检测
     * riskDetection：恶意图片检测
     */
    val serviceList: List<String>,
)

data class Response<T>(
    val successful: Boolean,
    val code: String,
    val message: String?,
    val data: T?,
)

data class ImageModerationData(val resultList: List<Result>) {
    /**
     * 检测结果
     */
    data class Result(
        val riskLevel: RiskLevel,
        val labelList: List<Label>?,
    )

    /**
     * 风险标签
     */
    data class Label(
        val label: String?,
        val confidence: String?,
        val description: String?,
    )

    /**
     * 风险等级
     */
    enum class RiskLevel(
        @JsonValue val value: String,
        val level: Int,
    ) {
        HIGH(value = "high", level = 3),
        MEDIUM(value = "medium", level = 2),
        LOW(value = "low", level = 1),
        NONE(value = "none", level = 0);

        fun isHigherThan(other: RiskLevel) = this.level > other.level
    }
}
