package team.aikero.murmuration.service.node.task.meitu.cutouts

import cn.hutool.core.bean.BeanUtil
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.builtin.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.service.node.shared.ImageListInput
import team.aikero.murmuration.service.node.shared.ImageListOutput
import team.aikero.murmuration.service.node.shared.SimpleImage
import team.aikero.murmuration.service.node.task.meitu.MeiTuParameterReq

/**
 * 扣图任务节点
 */
@NodeIdentifier(name = "美图抠图", supplier = Supplier.MEI_TU, ability = Ability.CUTOUTS)
class MeiTuCutoutsTaskNode : TaskNode<ImageListInput, MeiTuCutoutsTaskNodeParameter, ImageListOutput>() {

    override fun createTask(
        context: WorkflowNodeContext,
        input: ImageListInput,
        parameter: MeiTuCutoutsTaskNodeParameter
    ) {
        for (image in input.images) {
            taskManager.createTask(
                nodeInstanceId = context.node.id,
                supplier = Supplier.MEI_TU,
                ability = Ability.CUTOUTS,
                request = MeiTuCutoutsReq(
                    image.getUrl(),
                    BeanUtil.copyProperties(parameter, MeiTuParameterReq::class.java)
                )
            )
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): ImageListOutput {
        val images = taskResults.map {
            SimpleImage(it.url)
        }
        return ImageListOutput(images)
    }

}

class MeiTuCutoutsTaskNodeParameter : Parameter, MeiTuParameterReq()
