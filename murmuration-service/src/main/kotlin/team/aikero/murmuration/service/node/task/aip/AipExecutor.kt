package team.aikero.murmuration.service.node.task.aip

import team.aikero.blade.sequence.id.IdHelper
import team.aikero.murmuration.core.util.resolveGenericType
import team.aikero.murmuration.core.workflow.task.TaskResult
import kotlin.reflect.KClass
import kotlin.reflect.full.findAnnotation

/**
 * 算法调度平台执行器
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
abstract class AipExecutor<Input: Any, Output: Any>(protected val client: AipClient) {
    val outputType: KClass<Output> = resolveGenericType(this, AipExecutor::class, 1)
    val ability: AipAbility = this::class
        .findAnnotation<AipExecutorIdentifier>()
        ?.ability
        ?: throw IllegalStateException("算法调度平台执行器[${this::class}]缺少`@AipExecutorIdentifier`注解")

    fun createTask(request: Input): String {
        val taskId = IdHelper.getId().toString()
        client.createAiTask(
            ability = ability,
            taskId = taskId,
            params = request
        )
        return taskId
    }

    fun getTask(taskId: String): TaskResult<Output> {
        val result = client.getAiTask(taskId, outputType)

        if (result is TaskResult.Completed<Output>) {
            try {
                validateTaskOutput(result.value)
            }
            catch (ex: InvalidTaskOutputException) {
                return TaskResult.Failed("算法调度平台任务[${ability}]结果校验不通过: ${ex.reason}")
            }
        }

        return result
    }

    @Throws(InvalidTaskOutputException::class)
    open fun validateTaskOutput(output: Output) {}
}

class InvalidTaskOutputException(val reason: String): Exception()
