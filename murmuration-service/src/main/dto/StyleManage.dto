export team.aikero.murmuration.service.material.entity.StyleManage
    -> package team.aikero.murmuration.core.material.dto

specification StyleManagePageReq {
    styleCode
    styleName
    creatorId
    ge(createdTime)
    le(createdTime)
}

StyleManagePage {
    id
    styleCode
    styleName
    applicationBusiness
    creatorId
    creatorName
    createdTime
    imageMaterial
}

input StyleManageDefinedReq {
    id
    styleCode?
    styleName
    applicationBusiness
    imageMaterial
}

specification StyleManageListReq {
    applicationBusiness
}

StyleManageListVo {
    id
    styleCode
    styleName
}
