package team.aikero.murmuration.ai.aigc_image

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.service.node.task.aigc_image.GraphicStorehouse
import team.aikero.murmuration.service.node.task.aigc_image.GraphicStorehouseRepository

@Disabled("开发阶段使用")
@SpringBootTest(classes = [MurmurationApplication::class])
class GraphicStorehouseTest {

    /**
     * 推送图案库
     */
    @Test
    fun push(
        @Autowired repository: GraphicStorehouseRepository,
        @Autowired sql: KSqlClient,
    ) = withSystemUser {
        val imageId = repository.insert(
            imageUrl = "",
            tenantId = 2,
        )
        sql.deleteById(GraphicStorehouse::class, imageId)
    }
}
