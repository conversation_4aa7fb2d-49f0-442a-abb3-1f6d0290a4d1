package team.aikero.murmuration.ai.aip

import org.assertj.core.api.Assertions.assertThat
import org.awaitility.Awaitility.await
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.aip.comfyui.ComfyUiExecutor
import team.aikero.murmuration.service.node.task.aip.comfyui.ComfyUiInput
import team.aikero.murmuration.service.node.task.aip.comfyui.ComfyUiOutput
import team.aikero.murmuration.service.node.task.aip.kontextpod.FluxKontextExecutor
import team.aikero.murmuration.service.node.task.aip.kontextpod.FluxKontextInput
import team.aikero.murmuration.service.node.task.aip.kontextpod.FluxKontextOutput
import team.aikero.murmuration.service.node.task.aip.upscale.UpScaleExecutor
import team.aikero.murmuration.service.node.task.aip.upscale.UpScaleInput
import team.aikero.murmuration.service.node.task.aip.upscale.UpScaleOutput
import java.util.concurrent.TimeUnit

@Disabled("开发阶段使用")
@SpringBootTest(classes = [MurmurationApplication::class])
class AipExecutorTest {

    /**
     * 超分
     */
    @Test
    fun upScale(@Autowired executor: UpScaleExecutor) {
        // 创建任务
        val taskId = executor.createTask(
            UpScaleInput(
                inputImage = "https://chuangxin-oss-cdn.tiangong.tech/08d226a46d0f31e1881ff56caddc7682.png",
                scalefactor = 4,
            )
        )

        // 查询任务
        await()
            .atMost(30, TimeUnit.SECONDS)
            .pollDelay(1, TimeUnit.SECONDS)
            .pollInterval(1, TimeUnit.SECONDS)
            .until {
                executor.getTask(taskId).isCompleted()
            }

        // 验证结果
        val asyncResult = executor.getTask(taskId) as TaskResult.Completed<UpScaleOutput>
        val output = asyncResult.value
        assertThat(output.resImgs).isNotEmpty()
    }

    /**
     * 元素化衍生
     */
    @Test
    @Disabled("耗时太久了")
    fun elementalDerivation(@Autowired executor: ComfyUiExecutor) {
        // 创建任务
        val taskId = executor.createTask(
            ComfyUiInput(
                image = "https://oss-datawork-cdn.tiangong.tech/ai_images/server/comfyui/2025063015_3693618586259437985_7338090099332150584_00004_.png",
                batchSize = 4,
                workflowType = "JV-yuansuhuayansheng-v2.0",
            )
        )

        // 查询任务
        await()
            .atMost(5, TimeUnit.MINUTES)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(20, TimeUnit.SECONDS)
            .until {
                executor.getTask(taskId).isCompleted()
            }

        // 验证结果
        val asyncResult = executor.getTask(taskId) as TaskResult.Completed<ComfyUiOutput>
        val output = asyncResult.value
        assertThat(output.resImgs).isNotEmpty()
    }

    /**
     * 姿势裂变
     */
    @Test
    @Disabled
    fun kontextPod(@Autowired executor: FluxKontextExecutor) {
        // 创建任务
        val taskId = executor.createTask(
            FluxKontextInput(
                images = "https://oss-datawork-cdn.tiangong.tech/ai_images/server/fg_facerepair/3693618586259440369_d1110aab7bd50e714e3fdfb8a7587f3d.png",
                prompt = "正面站立，双臂交叉于身前，头部微侧，表情放松带些许俏皮，整体氛围随性自在",
                aspectRatio = "1:1",
                workflowType = "liblib_API",
            )
        )
        println("kontextPod taskId=${taskId}")

        // 查询任务
        await()
            .atMost(6, TimeUnit.MINUTES)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(20, TimeUnit.SECONDS)
            .until {
                executor.getTask(taskId).isCompleted()
            }

        // 验证结果
        val asyncResult = executor.getTask(taskId) as TaskResult.Completed<FluxKontextOutput>
        val output = asyncResult.value
        assertThat(output.resImgs).isNotEmpty()
    }
}
