package team.aikero.murmuration.component

import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.service.component.ImageInfoFetchException
import team.aikero.murmuration.service.component.ImageInfoFetcher

@SpringBootTest(classes = [MurmurationApplication::class])
class ImageInfoFetcherTest(@Autowired val imageInfoFetcher: ImageInfoFetcher) {

    @Test
    fun fetchSuccessful() {
        val (height, weight) = imageInfoFetcher.fetch("https://chuangxin-oss-cdn.tiangong.tech/08d226a46d0f31e1881ff56caddc7682.png")
        assertThat(height).isEqualTo(1030)
        assertThat(weight).isEqualTo(841)
    }

    @Test
    fun fetchFailed() {
        assertThatThrownBy {
            imageInfoFetcher.fetch("https://chuangxin-oss-cdn.tiangong.tech/08d226a46d0f31e1881ff56caddc7682.png1")
        }.isInstanceOf(ImageInfoFetchException::class.java)
    }
}
