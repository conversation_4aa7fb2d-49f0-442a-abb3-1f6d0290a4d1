package team.aikero.murmuration.task.handler

import org.awaitility.Awaitility
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.common.enums.task.Resolution
import team.aikero.murmuration.common.req.task.KontextPodRequest
import team.aikero.murmuration.common.req.task.UpScaleRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.workflow.task.TaskManager
import team.aikero.murmuration.service.node.task.meitu.MeiTuParameterReq
import team.aikero.murmuration.service.node.task.meitu.cutouts.MeiTuCutoutsReq
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * 任务管理器测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = [MurmurationApplication::class])
class TaskManagerTest {

    /**
     * 测试创建任务
     */
    @Test
    fun `test create task`(@Autowired taskManager: TaskManager) = withSystemUser {
        val bizId = UUID.randomUUID().toString()
        val bizType = "UNIT_TEST"
        taskManager.createTask(
            bizId = bizId,
            bizType = bizType,
            Supplier.REALESRGAN,
            Ability.UPSCALE,
            UpScaleRequest(
                "https://chuangxin-oss-cdn.tiangong.tech/08d226a46d0f31e1881ff56caddc7682.png",
                Resolution.R_2K,
            ),
        )

        // 查询任务
        Awaitility.await()
            .atMost(35, TimeUnit.SECONDS)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(5, TimeUnit.SECONDS)
            .until {
                taskManager.queryTaskStatus(bizId, bizType).isCompleted()
            }
    }

    /**
     * 测试创建姿势裂变任务
     */
    @Test
    @Disabled
    fun kontextPodTest(@Autowired taskManager: TaskManager) = withSystemUser {
        val bizId = UUID.randomUUID().toString()
        val bizType = "UNIT_TEST"
        taskManager.createTask(
            bizId = bizId,
            bizType = bizType,
            Supplier.COMFY_UI,
            Ability.POSTURAL_FISSION,
            KontextPodRequest(
                "https://oss-datawork-cdn.tiangong.tech/ai_images/server/comfyui/2025071111_3693618586259482663_7342012298610991105_00001_.png",
                "正面站立，头微低，左手抬起轻触脖颈，右手自然下垂，整体姿势随性放松"
            ),
        )

        // 查询任务，要3-5分钟
        Awaitility.await()
            .atMost(6, TimeUnit.MINUTES)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(10, TimeUnit.SECONDS)
            .until {
                taskManager.queryTaskStatus(bizId, bizType).isCompleted()
            }
    }


    /**
     * 测试创建美图抠图任务
     */
    @Test
    fun meiTuCutoutsTest(@Autowired taskManager: TaskManager) = withSystemUser {
        val bizId = UUID.randomUUID().toString()
        val bizType = "UNIT_TEST"
        taskManager.createTask(
            bizId = bizId,
            bizType = bizType,
            Supplier.MEI_TU,
            Ability.CUTOUTS,
            MeiTuCutoutsReq(
                "https://oss-datawork.oss-cn-hangzhou.aliyuncs.com/ai_images/server/UPscale/3693618586259454603.png",
                MeiTuParameterReq().apply {
                    nMask = false
                    modelType = 2
                    nbox = true
                }
            ),
        )

        // 查询任务
        Awaitility.await()
            .atMost(60, TimeUnit.SECONDS)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(5, TimeUnit.SECONDS)
            .until {
                taskManager.queryTaskStatus(bizId, bizType).isCompleted()
            }
    }
}
