import java.util.*

plugins {
    alias(commonLibs.plugins.springboot)
    alias(commonLibs.plugins.kotlin.spring)
    alias(commonLibs.plugins.kotlin.jvm)
    alias(commonLibs.plugins.common.conf)
    alias(commonLibs.plugins.google.ksp)
}

dependencies {
    implementation(projects.murmurationCommon)
    implementation(projects.murmurationSdk)
    implementation(projects.murmurationRuntime)

    // Cloud 组件
    implementation(commonLibs.blade.web.cloud.spring.boot.starter)
    implementation(commonLibs.blade.file.spring.boot.starter)
    // 用户权限管理
    implementation(commonLibs.blade.auth.spring.boot.starter)
    // 缓存
    implementation(springBootLibs.spring.springBootStarterCache)
    implementation(commonLibs.blade.data.redis.spring.boot.starter)
    // 协程
    implementation(commonLibs.kotlinx.coroutines.core)
    // ORM
    implementation(commonLibs.blade.jimmer.spring.boot.starter)
    runtimeOnly(commonLibs.mysql.connector)

    testImplementation(commonLibs.blade.test.spring.boot.starter)
    testImplementation(springBootLibs.junit.junitJupiterEngine)
    testRuntimeOnly(springBootLibs.junit.junitPlatformLauncher)

    ksp(commonLibs.jimmer.ksp)
    implementation(commonLibs.hutool.all)
    testImplementation("org.awaitility:awaitility-kotlin:4.3.0")
    implementation("org.eclipse.jetty:jetty-client:12.0.23")
    implementation("commons-codec:commons-codec:1.18.0")
    implementation("tech.tiangong.aigc:aigc-image-sdk:0.0.4-RELEASE")
    implementation("tech.tiangong.fashion:aigc-digital-print-sdk:3.0.5")
    implementation("com.lazada.lazop:lazop-api-sdk:1.2.2")
    implementation("net.coobird:thumbnailator:0.4.20")
    implementation("org.apache.rocketmq:rocketmq-client-java:5.0.8")
    implementation("com.aliyun:rocketmq20220801:3.1.3") {
        exclude(group = "pull-parser", module = "pull-parser")
    }
    implementation(commonLibs.dingtalk.java.sdk)
    implementation("team.aikero.pigeon:pigeon-sdk:0.0.4")
    implementation("tech.tiangong.aigc:aigc-image-sdk:0.0.4-RELEASE")
    ksp(projects.murmurationKsp.murmurationKspService)
}

tasks.test {
    useJUnitPlatform()

    val envFile = rootProject.file(".env")
    if (envFile.exists()) {
        val properties = Properties()
        envFile.reader().use { properties.load(it) }
        properties.forEach {
            val key = it.key.toString()
            val value = it.value
            systemProperty(key, value)
        }
    }
}
