# Changelog

Traces of your evolution, detailed below.

## [0.0.1-4] - 2025-08-07

### 🚀 Features

- *(scheduler)* 调整指数退避策略的参数配置 将指数退避策略的最大延迟时间从10分钟调整为13分钟，并将最大重试次数从20次增加到30次。这些调整使系统在任务调度失败时能提供更长的恢复窗口和更多的重试机会。 测试用例也相应更新以匹配新的配置参数。
- *(backoff)* 增加指数退避策略的最大退避时间和最大退避次数 将指数退避策略的最大退避时间从13分钟调整为30分钟，同时将最大退避次数从30次增加到60次。这一变更使得系统在遇到持续性问题时能够保持更长时间的退避重试机制。 测试代码中的相关参数也相应更新以匹配新的配置值。

### 🚜 Refactor

- *(scheduler)* 修改延迟队列的take方法以支持超时返回null 将DelayedQueue接口及其实现RedissonDelayedQueue的take方法返回值改为可空类型，并在Redisson实现中使用pollAsync替代takeAsync以支持超时机制。同时在DelayedQueueScheduler中添加对null返回值的处理。 test(backoff): 更新指数退避策略测试用例中的抖动策略 将测试用例中的抖动策略从NONE改为EQUAL以匹配实际使用场景。 test(graph): 修正模型图像生成节点的输入参数名 将测试用例中的参数名从"text"改为"prompt"以与实际接口保持一致。

## [0.0.1-3] - 2025-08-01

### 🚀 Features

- *(task)* 添加任务创建失败的重试机制 在TaskExecutor中新增对TaskStatus.PREPARED状态的处理，将其与RUNNING状态同样视为需要重试的状态。新增TaskCreationRetryableException异常类，用于在任务创建失败但可重试时抛出。 在YouChuanClient中处理下游限流情况：当遇到"同时任务数上限"或"Max_Concurrent_Limited"错误时，抛出TaskCreationRetryableException以便触发重试机制。
- *(try-on)* 重构虚拟换衣任务节点以支持图套分组 修改了Lazada、潮际等虚拟换衣任务节点的实现逻辑，将原有的单件衣服匹配机制改为图套分组匹配。新增了`groupByKit`扩展函数用于将款式图按图套分组，确保每个模特能匹配一个图套下的所有衣服。 同时优化了优川扩散任务处理器的提示词拼接顺序，将参考图URL移至提示词开头。
- *(youchuan/diffusion)* 添加参照风格图和角色/万物图支持
- *(youchuan/diffusion)* 支持多底图输入 修改YouChuanDiffusionRequest及相关处理逻辑，将单底图改为支持多底图列表输入
- *(youchuan/diffusion)* 重构底图参数为referenceImageUrl和bottomImageUrls

### 🐛 Bug Fixes

- *(lazada)* 处理 Lazada API 限流异常 在 LazadaClient 中新增对下游限流异常的处理逻辑。当 API 返回错误码 "ApiCallLimit" 时，抛出可重试的任务创建异常 TaskCreationRetryableException，以便工作流引擎能够进行自动重试。 同时优化了代码格式，移除了不必要的空行和尾部空格。
- *(comfyui)* 添加敏感图检测异常处理 在ElementalDerivationTaskHandler中增加对openBridgeClient.isSensitiveImage调用的异常捕获 当敏感图检测失败时记录错误日志并默认返回true以继续流程 修复了因敏感图检测服务异常导致的任务中断问题
- *(youchuan/diffusion)* 优化空字符串检查逻辑 使用isNotBlank替换null检查，提高代码健壮性
- *(youchuan/diffusion)* 修正图像衍生任务中的能力类型 将MODEL_IMAGE_DERIVATION改为IMAGE_DERIVATION以匹配实际能力定义

### 🚜 Refactor

- *(scheduler)* 重构指数退避策略实现 将指数退避策略从基于kotlin.time.Duration改为使用java.time.Duration和BigDecimal进行计算，提高精度和灵活性。主要变更包括： - 修改ExponentialBackoffConfig为可变类 - 重写ExponentialBackoffPolicy实现，直接使用BigDecimal进行计算 - 更新测试用例和配置类以适配新实现 - 移除对PigeonMessageClient的依赖 这些修改使得退避时间的计算更加精确，并支持更大的数值范围。同时简化了配置类的使用方式。
- *(task)* 修改试穿任务中的服装分组逻辑 调整了Lazada和ChaoJi试穿任务节点的服装分组机制，现在会先按图案分组，再在图套内部分组。这确保了同一图案下同一图套的所有服装只会分配给一个模特试穿，改进了原有的匹配机制。 修改涉及两个任务节点：LazadaTryOnTaskNode和ChaoJiTryOnTaskNode，均采用相同的分组逻辑优化

## [0.0.1-2] - 2025-07-30

### 🚜 Refactor

- *(db)* 调整表字段类型以支持更大数据量 将 node_instance_detail 表的 input 和 output 字段类型修改为 mediumtext， 并将 node_instance_history 表的 data 字段类型修改为 longtext， 以支持存储更大的 JSON 数据量。

## [0.0.1] - 2025-07-30

### 🚀 Features

- *(murmuration)* 添加工作流基础逻辑
- *(murmuration)* 简化实现任务调度 & 优化各类事件
- *(scheduler)* 引入执行器接口 - 新增 Executor 接口，定义工作执行标准 - 创建 WorkStatus 枚举，表示工作执行状态 - 将 TaskRunnable重构为 TaskExecutor 类，实现 Executor 接口
- *(task)* 重构任务执行逻辑与异步结果处理
- *(core)* 新增Component枚举定义组件类型 fix(workflow): 将WorkflowDefinition中component字段类型改为Component refactor(workflow): 修改NodeRegistry.getNode参数类型为Component fix(task): 处理TaskExecutor中AsyncResult.Failed的error可为空情况 feat(ai/aip): 新增算法调度平台(AIP)模块 - 新增AipAbility枚举定义能力类型 - 实现AipClient基础功能 - 添加AipExecutor抽象基类 - 实现超分功能(UpScaleExecutor)
- *(youchuan)* 禁用耗时的扩散测试 feat(aip): 优化AipExecutor能力初始化顺序 feat(component): 新增图片信息提取组件及测试
- *(aip/upscale)* 重构超分执行器并添加任务处理器 - 将UpScaleExecutor移至aip/upscale包 - 添加输出校验逻辑 - 实现UpScaleTaskHandler处理超分任务 - 添加REALESRGAN模型枚举 - 优化AipExecutor接口
- *(task)* 支持任务分组和立即完成机制 - 在TaskManager中新增group参数支持任务分组 - 新增TaskImmediatelyDoneException支持任务立即完成 - 在TaskExecutor中优化任务完成逻辑，提取公共方法 - 在UpScaleTaskHandler中实现无需超分时立即返回结果
- *(task)* 新增任务结果映射查询功能 refactor(YouChuanDiffusionTaskNode): 使用queryTaskAndMapResult重构状态查询 feat(ImageList): 扩展ImageList接口支持迭代和urls属性 feat(AsyncResult): 新增InternalError枚举类型 feat(UpScaleTaskNode): 新增超分任务节点实现
- *(task)* 新增任务超时处理机制 在Executor接口添加超时和失败通知方法 在TaskStatus枚举添加TIMEOUT状态 实现TaskExecutor的超时处理逻辑 新增TaskTimeoutEvent事件类型
- *(core)* 重构Ability枚举类，简化功能定义 feat(task): 修改TaskManager参数名nodeId为nodeInstanceId feat(aip): 新增ComfyUI相关实现类及测试 feat(param): 实现NodeImageList的图片获取逻辑 feat(model): 新增COMFY_UI模型类型 refactor(task): 统一使用ImageListInput/Output简化节点参数 feat(aip): 新增元素化衍生任务处理器和节点
- *(task)* 重命名TaskInstance.group为groupIndex refactor(entity): TaskDetail/TaskResult继承BaseEntity和OrgWithFilter feat(youchuan): 图片结果转存OSS feat(model): 新增LING_VISIONS_REIMAGE模型 feat(error): 新增TASK_CANCELED错误类型 docs(youchuan): 添加YouChuanClient类注释 feat(gradle): 添加blade-file和commons-codec依赖 feat(lingvisions): 新增灵图裂变客户端及任务处理器
- *(scheduler)* 实现任务调度器为基于 Redis 的退避调度 - 新增Backoff工具类及测试用例 - 新增WorkflowConfig配置类集成新调度器 - 更新TaskExecutor和TaskEventListener适配新调度器 - 新增集成测试验证调度器功能
- *(task)* 修改TaskDetail和TaskResult实体继承关系 fix(test): 启用TaskManagerTest中的测试用例
- *(workflow)* 重构任务节点执行逻辑 - 将AsyncResult.Done重命名为Completed - 新增NodeResult表示节点执行结果 - 重构TaskNode抽象类实现 - 修改相关测试用例适配新逻辑 refactor(task): 优化任务管理查询接口 - 拆分queryTaskAndMapResult为queryTaskStatus和queryTaskWithPassedResult - 新增审核通过结果过滤逻辑 feat(aigc-image): 新增贴图能力支持 - 新增AigcImageClient对接aigc-image服务 - 新增TextureTaskHandler处理贴图任务 - 新增TemplateKitTextureTaskNode实现版型图套贴图逻辑 chore(deps): 升级framework版本至3.1.3-SNAPSHOT
- *(aip)* 新增KontextPod姿势裂变功能 新增KontextPodExecutor及相关测试，支持姿势裂变任务处理
- *(lazada)* 新增Lazada API集成功能 - 添加Lazada SDK依赖和配置文件 - 实现Lazada API客户端和DTO - 添加Lazada服务测试用例
- *(ai/chaoji)* 新增潮际AI服务客户端及相关工具类
- *(meitu)* 新增美图API客户端及相关DTO
- *(ChaoJiDTO)* 将必填字段改为非空类型并调整类结构
- *(kontextpod)* 新增姿势裂变功能及相关模型 新增COMFY_UI_GPT模型和POSTURAL_FISSION能力 实现KontextPodTaskHandler处理器和KontextPodTaskNode节点 添加相关测试用例
- *(meitu)* 新增美图抠图功能及相关组件 新增美图抠图功能，包括： 1. 新增MEI_TU模型类型和CUTOUTS能力枚举 2. 新增MeiTuCutoutsTaskHandler任务处理器 3. 新增MeiTuCutoutsTaskNode工作流节点 4. 新增ImageUtil图片工具类 5. 修改相关DTO和客户端逻辑
- *(lazada)* 实现Lazada客户端AI换脸和换背景功能 新增ChangeFaceRequest和ChangeBackgroundRequest请求类 重构LazadaClient实现签名和通用请求逻辑 添加相关单元测试
- *(core)* 新增节点元数据注册功能
- *(lazada)* 新增Lazada虚拟换衣、换背景、换脸任务处理器和节点 - 新增LazadaChangeBackgroundTaskHandler/LazadaChangeBackgroundTaskNode处理换背景任务 - 新增LazadaChangeFaceTaskHandler/LazadaChangeFaceTaskNode处理换脸任务 - 新增LazadaTryOnTaskHandler/LazadaTryOnTaskNode处理虚拟换衣任务 - 重构LazadaClient支持任务状态查询 - 删除旧版LazadaApi相关代码 - 更新Ability和Supplier枚举
- *(workflow)* 新增工作流定义和实例相关功能 - 修正工作流实例名称字段为serialNumber - 添加节点实例错误信息字段 - 修改任务分组字段为Long类型 - 新增工作流、节点和任务相关REST接口 - 添加服务context-path配置 - 新增工作流、节点和任务相关DTO定义
- *(dto)* 新增 nodeMetadata 扁平化字段
- *(chaoji)* 重构潮际客户端与服务接口 refactor(chaoji): 删除SignatureUtil工具类 feat(chaoji): 新增潮际虚拟换衣任务处理器和节点 refactor(chaoji): 重构DTO结构，简化枚举定义 fix(lazada): 统一服装图字段命名从clothesImage改为clothingImage feat(core): 新增CHAO_JI供应商枚举 test(chaoji): 添加潮际客户端单元测试 refactor(youchuan): 简化JobStatus枚举定义 refactor(aip): 为TaskState添加@JsonValue注解 refactor(lingvisions): 简化BgColor枚举定义
- *(core)* 为所有任务节点添加 NodeIdentifier 解析参数信息
- *(kontextpod)* 移除调试日志并优化姿势裂变功能 - 移除MeiTuCutoutsTaskHandler中的调试日志 - 将姿势裂变功能从COMFY_UI_GPT迁移至COMFY_UI - 新增自动计算图片比例功能 - 移除废弃的COMFY_UI_GPT相关代码
- *(ai)* 使用NodeProperties注解替换注释文档 在YouChuanDiffusionTaskNode和LazadaTryOnTaskNode中，将输入输出类的注释文档替换为NodeProperties注解，提升代码可维护性
- *(core)* 新增PUSH_POD_STYLE能力类型 feat(task): 增加createCompletedTask方法用于创建已完成任务 refactor(kontextpod): 重构KontextPodTaskNode，支持输入图片信息传递 feat(param): 为Output接口添加Empty实现 refactor(node): 移除ASYNC节点类型 chore(app): 添加digital.print.client包到Feign扫描 refactor(handler): 重命名KontextPodReq为KontextPodRequest feat(image): 实现GraphicImage和TemplateImage的getUrl方法 refactor(entity): 将groupIndex改为非空Long类型 feat(core): 新增MURMURATION供应商类型 test(task): 更新测试用例使用KontextPodRequest chore(build): 添加aigc-digital-print-sdk依赖 feat(custom): 新增PushPodStyleNode用于推送POD选款
- *(style)* 新增风格管理模块及相关功能 - 新增SQL表结构style_manage - 添加DTO定义文件 - 实现实体类、业务枚举和控制器 - 支持风格的分页查询、增删改操作
- *(task)* 重构任务结果结构，统一使用url字段 - 移除TaskResult中的type和data字段，统一使用url字段 - 修改所有任务节点和处理器，适配新的TaskResult结构 - 将ChaoJiTryOnRequest等请求类移动到common模块 - 添加KSP处理器自动生成Feign客户端和Controller - 启用Jimmer的show-sql配置 - 添加任务状态变更的MQ通知功能
- *(style)* 实现风格管理与Moodboard集成
- *(style)* 新增风格管理列表功能
- *(workflow)* 实现工作流开始结束节点与通用数据处理 新增工作流核心节点实现（StartNode/EndNode）和通用数据结构GenericData - 添加StartNode处理工作流启动逻辑，记录用户信息和开始时间 - 添加EndNode处理工作流结束逻辑，记录结束时间 - 实现GenericData作为通用数据载体，支持多种数据格式转换和操作 - 完善Image接口及其实现类SimpleImage - 添加AutoSealedInterfaceModule自动处理sealed interface序列化 - 补充单元测试验证GenericData功能
- *(workflow)* 实现工作流图引擎
- *(config)* 工作流配置使用外部化属性 • 将调度器配置从代码迁移到 MurmurationProperties 配置类 • 使 RedisBackoffScheduler 类可继承以支持 RefreshScope 的 cglib 代理
- *(workflow)* 优化工作流定义列表查询并增强节点实例复核功能 • 在WorkflowDefinitionController中修改list方法，支持带条件的分页查询 • 在WorkflowInstance.dto中增加flat(nodeDefinition)字段，扩展节点定义信息 • 在NodeInstanceController中完善approve方法，审批后重新调度节点任务 这些改动提升了工作流查询的灵活性和节点任务处理的可靠性。
- *(feign-client)* 在创建任务接口中添加bizId参数 • 在TaskHandlerFeignClientSymbolProcessor中修改Feign客户端接口定义 • 为createTask方法添加@RequestParam注解的bizId参数 • 优化导入语句，使用通配符简化注解导入
- *(ksp-processor)* 添加节点控制器符号处理器并增强任务处理器 • 新增 TaskNodeControllerSymbolProcessor 用于生成节点 REST 控制器 • 优化 TaskHandlerRestControllerSymbolProcessor 支持枚举注释解析
- *(task)* 重试、批量结果处理、结果计数接口开发
- *(node)* 改进节点元数据注册逻辑
- *(task-handler)* 重构任务查询接口并添加敏感图检测 • 修改所有TaskHandler的query方法，增加request参数 • 移除TaskTimeoutEvent，统一使用TaskFailedEvent处理超时 • 为ElementalDerivationTaskHandler添加敏感图检测功能 BREAKING CHANGE: 所有TaskHandler实现类需要更新query方法签名
- *(workflow)* 优化工作流实例详情接口并添加任务分组功能 • 修改工作流实例详情接口为GET请求并过滤非任务类型节点 • 在任务分页接口中添加按groupIndex分组功能 • 完善多个控制器的接口文档注释 本次提交主要优化了工作流相关接口的功能和文档，重点改进了实例详情展示和任务分组查询能力。
- *(dingtalk)* 新增钉钉消息通知功能 在NodeReviewing事件中新增钉钉验收消息推送，包含DingTalkClient实现及相关配置修改
- *(task)* 添加批量更新任务结果验收状态功能 • 在TaskManager中新增updateAllTaskResultPassed方法，用于将指定节点实例的所有任务结果标记为"通过" • 修改TaskNode逻辑，当无需验收时自动调用批量更新方法 • 调整节点状态处理顺序，修复状态判断逻辑 该变更实现了任务节点在无需验收时的自动通过机制，简化了工作流处理逻辑。
- *(workflow)* 优化工作流实例和节点实例的创建逻辑
- *(workflow)* 移除节点类型过滤并优化空值处理
- *(任务系统)* 为任务创建添加业务类型支持 • 在任务创建接口和实体中新增bizType字段 • 更新相关测试用例验证业务类型功能 • 确保任务状态通知包含业务类型信息 此次修改扩展了任务系统的业务标识能力，通过增加业务类型字段(bizType)来支持更细粒度的任务分类和追踪。修改涉及任务创建接口、实体类、事件监听器及测试用例，确保系统各层面对业务类型的正确处理。
- *(workflow)* 添加工作流实例控制操作 • 在WorkflowInstanceController中新增暂停、恢复、取消和重试工作流的端点 • 在DatabaseGraphEngine中实现暂停工作流时同步暂停运行中的节点 • 所有操作均通过graphEngine调用对应方法并返回统一响应格式
- *(task)* 优化任务结果计数结构
- *(workflow)* 增强工作流失败节点的重置功能 • 在DatabaseGraphEngine中添加resetFailedNodes方法，完整重置失败节点及其关联数据 • 修改NodeInstance实体，添加任务实例和历史记录关联 • 更新TaskManager支持从API创建任务并查询状态 fix(rocketmq): 改进RocketMQ配置和消息处理 • 在RocketMQProperties中添加instanceId字段 • 引入TopicHolder统一管理主题名称 • 添加RocketMQAliyunClient支持阿里云消息查询 refactor(task): 重构任务管理相关代码 • 将任务创建逻辑从RestController移到TaskManager • 修改TaskInstance实体，用nodeInstance关联替代nodeInstanceId字段 • 优化RedisBackoffScheduler的take方法实现 test: 禁用部分集成测试 • 临时禁用NodeExecutionIntegrationTest的工作流执行测试 • 更新TaskManagerTest使用新的API任务创建方式 本次提交主要增强了工作流失败处理能力，改进了RocketMQ集成，并重构了任务管理模块的核心逻辑。关键变化包括完整的节点重置实现、统一的消息主题管理以及更清晰的任务API设计。
- *(meitu-cutouts)* 新增透明图判断及裁剪功能
- *(backoff)* 为退避机制添加抖动类型支持 • 在BackoffConfig中新增jitterType参数，默认为EQUAL
- *(node)* 优化节点执行逻辑 • 优化节点执行流程，增加工作流状态检查与日志记录 • 改进存储保存逻辑，使用UPDATE_ONLY模式提升性能
- *(workflow-config)* 添加节点元数据注册监听器并优化调度器配置
- *(workflow)* 新增任务节点事件监听器并重构节点监听逻辑 • 添加 TaskNodeEventListener 用于处理任务完成后的节点状态更新 • 重命名 NodeMetadataRegistrar 为 NodeMetadataRegistryListener 并移除 @Component 注解 • 优化 NodeEventListener 的消息发送逻辑，增加异常处理
- *(backoff-work)* 修改默认退避时间为全抖动策略 • 将默认的固定0秒退避时间改为[0,1000]毫秒区间的全抖动指数退避
- *(task-listener)* 添加节点任务完成状态检查 • 在TaskNodeEventListener中增加对节点下所有任务状态的检查 • 仅当所有任务完成/失败/取消时才继续处理节点事件
- *(scheduler)* 添加任务详细日志
- *(workflow)* 优化任务节点的事务处理逻辑 - 自调用无法代理
- *(workflow)* 调整模块、分包，明确领域边界
- *(workflow)* 优化工作流暂停机制并添加控制台功能 • 将工作流状态"WAITING"重命名为"PAUSED"以更准确表达状态含义 • 简化暂停逻辑，移除节点状态变更操作 • 新增控制台接口用于重新提交运行中任务 修复任务执行时工作流暂停状态检查问题，并优化Redis调度器处理逻辑
- *(workflow)* 添加节点事件监听并优化工作流控制 • 在ConsoleController中添加重试失败工作流接口和响应封装 • 引入NodeEventListener记录节点状态变更事件 • 移除NodeExecutor中冗余的工作流实例存储逻辑 • 重命名nodeEventListener为nodeSchedulerListener以明确职责
- *(backoff)* 改进指数退避算法实现 • 将底数从固定值2改为可配置的BigDecimal参数 • 优化BackoffConfig默认参数（base=1.6, maxTimes=20） • 修复BackoffWork中随机抖动的实现问题 相关测试用例已同步更新
- *(workflow)* 新增任务管理器和数据库图引擎配置 • 在WorkflowConfiguration中添加TaskManager和DatabaseGraphEngine的Bean配置 • 将TaskManager和DatabaseGraphEngine改为open类以支持扩展 • 优化节点调度器注解并更新相关事件监听器注释
- *(workflow)* 优化任务排序 • 在WorkflowInstanceController中新增recover接口用于恢复运行中的工作流 • 为TaskInstance.results添加orderedProps确保结果按id排序
- *(workflow)* 添加任务编号参数到钉钉通知URL
- *(StyleManage)* 重构列表接口并更新DTO结构 将GET列表接口改为POST，新增StyleManageListReq请求体和StyleManageListVo响应体
- *(workflow)* 优化节点审核流程和状态处理 • 新增REVIEWED状态并调整TaskNode处理逻辑 • 修改NodeInstanceController.approve方法直接处理审核通过节点 • 更新NodeExecutor.isNodeCanExecute检查逻辑以支持新状态 • 移除BackoffScheduler不必要的锁操作 本次修改主要优化了工作流节点的审核流程，将审核通过后的处理从入队改为直接执行，同时引入REVIEWED状态明确区分审核中和已审核状态。相关组件包括TaskNode、NodeInstanceController和NodeExecutor都进行了相应调整。
- *(task-engine)* 优化任务节点监听与工作流检查逻辑 • 移除TaskNodeEventListener的@Component注解，改为手动管理 • 调整DatabaseGraphEngine中工作流完成检查的执行顺序 • 确保就绪节点检查后再验证工作流状态，避免冗余操作
- *(workflow-engine)* 调整表达式计算成功日志并优化节点执行日志时机 • 删除WorkflowExpressionEvaluator中的表达式计算成功调试日志 • 在NodeExecutor中添加更详细的节点表达式计算日志，包含节点ID和键值信息 • 保持错误日志记录不变以确保问题追踪能力 修改旨在减少冗余日志输出同时提升节点执行时的调试信息可读性
- *(task-node)* 添加任务重试时更新节点状态的功能 close: *********** • 新增TaskPrepareEvent监听器处理任务重试场景 • 当节点处于验收状态时自动更新为运行中状态
- *(MeiTuCutoutsTaskHandler)* 添加美图任务日志记录
- *(node)* 优化美图抠图任务处理逻辑 - 在KontextPodTaskNode中更新注释变量名promptList为prompts - 重构MeiTuCutoutsTaskHandler，简化透明图处理流程并移除冗余裁剪逻辑
- *(workflow)* 为节点验收事件添加节点能力标题显示 在NodeSchedulerListener中，将event.node提取为变量以提高可读性，并在发送通知时添加node.ability.title字段。 同时重构Ability枚举类，为每个能力添加中文标题属性，用于在前端展示更友好的能力名称。 这些修改使得在节点验收通知中可以显示更详细的节点信息，提升用户体验。
- *(node)* 允许工作流在部分失败状态下继续执行节点 修改了 NodeExecutor.kt 中的 isWorkflowRunning 方法，将 WorkflowStatus.FAILED 状态也视为工作流正在运行的状态。这使得在部分工作流节点失败的情况下，其他节点仍能继续执行。 同时修复了 catch 块的代码格式问题，使其符合项目代码风格规范。 这个变更使得工作流执行更加灵活，允许部分失败的情况下继续执行其他节点，而不是立即停止整个工作流。
- *(expression)* 支持集合相加运算并优化表达式处理 新增对集合相加运算的支持，包括： - 添加 ListOperatorOverloader 实现集合的加减运算 - 优化表达式处理逻辑，支持复合表达式标准化 - 新增 containsExpressions 和 isSimpleExpression 方法 - 更新文档说明新增的集合运算功能 测试用例： - 添加表达式标准化测试 - 添加集合相加运算测试 - 添加不同集合相加测试
- *(workflow)* 优化列表操作符重载实现并更新测试用例 重构了 ListOperatorOverloader 类，使其直接实现 OperatorOverloader 接口而非继承 StandardOperatorOverloader。 简化了列表加减法操作的实现，直接使用 Kotlin 集合操作符 + 和 - 替代手动操作 ArrayList。 更新了 GraphEngineTest 中的测试用例，将 images 输入从单一表达式改为合并两个表达式的加法操作。 这些修改使代码更加简洁且符合 Kotlin 惯用写法，同时保持了原有功能不变。
- *(kontext)* 切换 flux kontext 模型 主要变更包括: - 类名从KontextPod变更为FluxKontext - 输入参数image改为images - 移除了JsonProperty注解 - 新增必填字段workflowType - 更新了AipAbility中的COMFY_UI描述
- *(aip)* 更新FluxKontextExecutor的AipAbility标识符 将FluxKontextExecutor的注解标识符从COMFY_UI更改为COMFY_UI_GPT，并在AipAbility枚举类中新增COMFY_UI_GPT枚举项，以区分不同的ComfyUI能力类型。
- *(entity)* 为StyleManage实体添加TenantId接口继承 在StyleManage实体类中新增了TenantId接口的继承，以支持多租户功能。这将确保风格管理数据能够按租户进行隔离。 该修改是为了满足系统在多租户环境下的数据隔离需求。
- *(core)* 为工作流执行添加错误上报功能 在NodeSchedulerListener、TaskExecutor和NodeExecutor中添加错误上报逻辑，当工作流节点或任务执行失败时，将错误信息通过ErrorReportEvent上报。同时添加了钉钉SDK依赖以支持错误通知。 修改内容包括： 1. 在节点验收失败时上报错误信息 2. 在任务执行失败时上报错误信息 3. 在节点执行失败时上报错误信息 4. 添加钉钉SDK依赖用于错误通知
- *(workflow)* 重构错误报告上下文信息并添加枚举注释支持 重构了NodeSchedulerListener、NodeExecutor和TaskExecutor中的错误报告事件，将上下文信息中的英文键名改为中文以提高可读性。 同时移除了错误来源中的"murmuration:"前缀。 新增了EnumComment注解和对应的KSP处理器，用于为枚举类生成注释映射和扩展属性。 该功能已应用于Supplier和Ability枚举，可通过.comment属性获取枚举项的注释。 调整了项目结构，将KSP相关模块统一移动到murmuration-ksp目录下。
- *(scheduler)* :sparkles: 为DelayedQueueScheduler添加OpenTelemetry上下文支持 在DelayedQueueScheduler的enqueue方法中添加了OpenTelemetry上下文传播支持，确保异步任务能够正确继承调用链的追踪上下文。同时更新了build.gradle.kts以引入必要的OpenTelemetry依赖项。 该修改保证了分布式追踪系统能够正确关联调度器内部执行的异步任务与外部调用链。
- *(scheduler)* 为延迟队列调度器添加OpenTelemetry支持 在DelayedQueueScheduler中集成OpenTelemetry追踪功能： 1. 新增traceContext字段到DelayedTask数据结构 2. 添加上下文注入(inject)和提取(extract)方法 3. 在执行任务时传播OpenTelemetry上下文 同时优化TaskExecutor对非法任务状态的处理，增加日志记录
- *(rest-client)* 支持自定义拦截器配置 - 将 RestClient 配置中的 `configure` 参数重命名为更明确的 `interceptor` - 在 RestClientFactoryBean 和 RestClients 工具类中添加拦截器配置支持 - 更新 RestClientBeanRegistrar 以处理拦截器配置参数 - 完善相关类和方法注释，使其更清晰
- *(workflow)* 新增工作流事件通知功能并重构事件处理逻辑 本次提交主要包含以下变更： 1. 新增PigeonNotifyListener用于处理工作流节点审核和完成事件的通知 2. 重构NodeSchedulerListener，移除了消息通知相关逻辑 3. 新增WorkflowWork类作为工作流工作单元 4. 重构NodeWork相关方法名，将toNodeWork改为toWork 5. 完善工作流引擎中的事件发布逻辑，确保工作流状态变更时发布相应事件 6. 优化节点参数处理逻辑，增加对动态调整节点供应商和参数的兼容性 这些变更使得工作流事件处理更加模块化，通知功能集中到专门的监听器中处理，同时增强了系统的扩展性和可维护性。
- *(task)* 重构优川扩散任务请求结构，支持独立参数传递 将优川扩散任务的请求参数从拼接字符串改为结构化字段： 1. 将原text字段重命名为prompt，提高语义清晰度 2. 新增referenceImageUrl字段单独处理参考图URL 3. 新增moodboardId字段单独处理风格ID 4. 修改相关任务节点和处理器逻辑以适配新结构 这种改进使得参数传递更加清晰，避免了字符串拼接的维护问题，并为后续功能扩展提供了更好的基础
- *(controller)* 添加创建工作流定义的API接口
- *(scheduler)* :sparkles: 添加队列查看功能 在调度器系统中新增队列查看功能，包括： 1. 在DelayedQueue接口中添加readAll方法用于读取所有元素 2. 在Scheduler接口中添加listAll方法用于列出所有工作 3. 实现RedissonDelayedQueue中的readAll方法 4. 在DelayedQueueScheduler中实现listAll方法 5. 在ConsoleController中添加/queue-view端点用于查看队列状态 这些改动允许管理员通过API查看当前队列中的工作流节点和任务状态
- 初始化项目基础架构文档
- *(workflow)* 为工作流实例接口添加权限校验 fix(entity): 将WorkflowDefinition的OrgWithFilter替换为OrganizationId refactor(style): 优化风格删除接口实现方式
- *(sql)* 在prod_print_task表中添加style_code字段
- *(workflow)* 为工作流实体添加@Key注解并简化控制器保存逻辑 - 在WorkflowDefinition实体中为name字段添加@Key注解 - 在Edge实体中为workflowDefinition、sourceNodeKey和targetNodeKey字段添加@Key注解 - 简化WorkflowDefinitionController中的保存逻辑，移除不必要的保存模式设置
- *(console)* 添加运行中任务查询接口及环境主题处理优化 为控制台添加新接口/running-tasks，支持通过任务编号查询运行中的节点实例和任务实例，并返回任务处理器的查询结果。同时优化TopicHolder中环境变量的处理逻辑，使用substringBefore替代removeSuffix以增强兼容性。 在NodeInstance实体类中为NodeStatus枚举添加@EnumComment注解，完善状态字段的文档注释。
- *(scheduler)* 添加MDC日志追踪支持 为DelayedQueueScheduler添加MDC日志上下文支持，通过withMDC方法自动注入node_id/task_id到日志上下文 修改ConsoleController返回视图增加实例ID字段 新增logback-spring.xml配置文件定义日志输出格式包含MDC变量
- *(task)* 为任务查询添加MDC上下文追踪 在ConsoleController中为任务查询添加了MDC上下文追踪功能，使用task_id作为追踪标识。通过MDC.putCloseable自动管理资源，确保线程上下文中的task_id在查询完成后被正确清理。 这一改进有助于在日志系统中追踪特定任务的执行流程，便于问题排查和日志分析。
- *(db)* 为任务结果表添加创建时间字段并优化数据库操作方式 为任务结果表(task_instance_result)添加created_time字段，用于记录数据创建时间。同时将多处数据库更新操作从传统的executeUpdate方式改为使用Jimmer的save方法，提高代码可读性和一致性。 涉及的修改包括： 1. 在table.sql中为task_instance_result表添加created_time字段 2. 修改DatabaseGraphEngine中节点状态和工作流状态的更新方式 3. 修改NodeExecutor中存储和节点输出的保存方式 4. 使TaskInstanceResult实体继承CreatedTime接口

### 🐛 Bug Fixes

- *(murmuration)* 修复应用名称配置错误
- *(lingvisions/reimage)* 实现任务节点运行和状态查询逻辑 feat(lingvisions/reimage): 添加灵图裂变任务循环创建功能 refactor(youchuan/diffusion,lingvisions/reimage): 简化查询方法，直接使用context参数
- *(task)* 使用 withSystemUser 包装任务执行方法
- *(task)* 修复TaskExecutor中任务实例参数传递问题
- *(task)* 修复任务实例明细字段查询问题
- *(RedisBackoffScheduler)* 改进任务获取逻辑并处理异常 • 将阻塞式 `takeAsync` 改为轮询式 `pollAsync` 以避免长时间阻塞 • 添加对 RedissonShutdownException 和 InterruptedException 的处理 • 在异常发生时抛出 CancellationException 以正确取消协程
- *(workflow-definition)* 优化工作流定义列表查询逻辑为前端过滤非 task 节点
- *(workflow-engine)* 更新数据库图引擎的保存模式 • 在DatabaseGraphEngine.kt中添加AssociatedSaveMode.APPEND参数 • 修改保存实例时的关联保存行为 • 保持与jimmer框架的最新特性兼容
- *(graph-engine)* 修复工作流引擎存储和节点执行逻辑
- *(dingtalk)* 使用配置项替换硬编码的URL
- *(RedisBackoffScheduler)* 优化Redis任务调度异常处理 • 简化take()方法逻辑，移除冗余循环和异常捕获 • 在offerAsync操作中添加RedissonShutdownException处理，统一转换为CancellationException
- *(task)* 修复任务重试状态检查逻辑 • 将重试前的状态检查条件从!isFinished()改为isFinished( • 确保只有已完成的任务才能被重试 • 修复错误提示信息中的逻辑矛盾
- *(config)* 取消注释jimmer配置并移除调试日志 • 在nacos配置列表中取消jimmer-conf的注释 • 删除jimmer语言和sql日志输出配置 • 移除team.aikero.murmuration包的debug日志级别
- *(task-controller)* 修复分页结果计算错误
- *(NodeInstanceController)* 添加节点提交前的状态检查 • 在提交节点前检查状态是否为REVIEWING • 防止非审核状态的节点被错误提交 • 增强系统对节点状态变更的健壮性
- *(workflow)* 修复事务代理导致的bean注入问题 • 移除WorkflowConfiguration中的@Bean注解，改为直接使用@Component • 将DatabaseGraphEngine和TaskManager改为@Component注解类 • 添加注释说明cglib代理导致的构造函数属性为空问题 该修改解决了因事务代理导致的bean初始化问题，确保依赖注入正常工作。
- *(workflow)* 改进任务和节点执行失败时的错误处理 • 将错误信息参数从String改为Exception类型，以记录完整错误堆栈 • 添加errorStackTrace字段到TaskInstanceDetail和NodeInstanceDetail实体 • 实现updateTaskStatusWithException和updateNodeStatusWithException方法处理长错误信息 这些修改增强了错误追踪能力，确保即使错误信息超长也能保存关键信息
- *(task-node)* 启用TaskNodeEventListener组件注解 • 添加@Component注解以激活任务节点事件监听器 • 确保监听器能被Spring容器正确管理 • 修复因缺少注解导致的事件监听失效问题
- *(aigc-image)* 修复模板纹理任务节点中的租户ID获取问题 • 将硬编码的租户ID替换为从CurrentUserHolder动态获取 • 优化任务控制器的内存分页逻辑，增加代码可读性 • 分离分组操作与分页操作，提升性能 修改涉及模板纹理任务节点的租户ID处理方式，确保多租户场景下的数据隔离。同时重构任务列表的分页逻辑，使代码结构更清晰。
- *(workflow)* 将TaskInstanceResult的passed默认值改为false close: QX-00009910 • 修改passed字段的@Default注解值从true变为false • 调整默认行为以匹配验收流程的实际需求 • 确保任务实例初始状态更符合业务逻辑
- *(task)* 将任务分组索引类型从Int改为Long close: QX-00009911,QX-00009913 • 修改KontextPodTaskNode中groupIndex参数类型为Long • 更新TaskManager.createTask方法签名及内部处理逻辑 • 调整TaskController中相关接口的字段类型
- *(core)* 优化事件发布日志信息
- *(event)* 修改PigeonNotifyListener中的消息类型标识
- *(workflow)* 修改节点元数据引用方式为直接设置供应商和能力 在GraphEngineTest测试类中，将所有通过findNodeMetadataBySupplierAndAbility查询节点ID的方式改为直接设置supplier和ability属性。同时在WorkflowDefinitionController中为NodeDefinition的nodeMetadata关联设置仅保存键引用。 这些修改优化了节点元数据的处理逻辑，使代码更清晰且减少不必要的查询操作。
- *(scheduler)* 增强延迟队列调度器的错误处理 在DelayedQueueScheduler中为任务执行添加try-catch块，捕获并记录执行异常。同时将未知错误的日志消息描述从"occurred an error"改为更明确的"occurred an unknown error"。 这些修改提高了系统的健壮性，确保单个任务失败不会影响整个调度器运行，同时使错误日志更具可读性。

### 💼 Other

- 创建 murmuration 项目初始结构
- 移除本地环境配置并调整测试依赖
- *(runtime)* 移除springboot插件依赖
- 模块重命名，修正构建项启动问题
- *(ksp)* 添加murmuration-ksp-service模块的Gradle配置 新增murmuration-ksp-service模块的build.gradle.kts文件，包含基础插件配置和KSP API依赖
- *(gradle-wrapper)* 更新Gradle分发URL至腾讯云镜像 将Gradle分发URL从官方地址改为腾讯云镜像地址，并升级Gradle版本从8.13至8.14.3
- *(deps)* :arrow_up: 升级框架和SDK版本 将 frameworkVersion 从 3.1.3-SNAPSHOT 升级至 3.1.4 同时将 aigc-digital-print-sdk 从 3.0.5-SNAPSHOT 更新为正式版 3.0.5 此次更新主要涉及依赖版本升级，移除了快照版本依赖，使用稳定版本

### 🚜 Refactor

- *(aip/AipClient)* 移除未使用的createUpScaleTask相关代码
- 重命名 Model 为 Supplier，意为能力供应方
- *(event-publisher)* 重构事件发布机制为依赖注入模式 • 将EventPublisher由object改为class并通过构造函数注入ApplicationEventPublisher • 修改所有调用点使用注入的eventPublisher实例 • 更新相关测试和配置类以适配新的事件发布机制 fix(task-status): 修正任务状态判断逻辑 • 在TaskStatus中为TIMEOUT状态添加isFinished判断 • 确保超时任务能正确触发状态变更和事件通知 test(task-controller): 完善任务状态通知测试 • 重构UpScaleTaskControllerTest验证MQ消息内容 • 添加对消息体状态字段的断言检查 • 使用真实MQ客户端进行端到端测试 chore(sql): 调整SQL脚本存放路径 • 将数据库初始化脚本移动到docs/sql/init目录 • 更新aigc_digital_print.sql字段约束条件 本次重构主要解决了EventPublisher的硬编码问题，改为更灵活的依赖注入模式，同时修复了任务状态机逻辑并增强了测试覆盖。
- *(workflow)* 调整工作流节点事件体 • 删除旧的WorkflowEngine实现，迁移到DatabaseGraphEngine • 修改NodeEvent使用NodeWork替代NodeInstance作为事件主体 • 添加NodeWork与NodeInstance的转换方法，优化事件处理流程 涉及核心工作流引擎重构和节点事件处理优化
- *(rocketmq)* 重构RocketMQ客户端实现并移除旧SDK • 删除废弃的TaskHandlerClient接口 • 引入新的RocketMQClient封装标准Java客户端 • 统一所有MQ相关操作到新的RocketMQ模块 • 更新测试用例使用新的客户端API 涉及组件：murmuration-sdk, murmuration-service
- *(task-requests)* 重构任务请求相关代码结构 • 删除废弃的TaskRequest注解类 • 将请求DTO类移动到common模块的req.task包下 • 将枚举类移动到common模块的enums.task包下 本次重构主要优化了代码组织结构，将分散在各模块的任务请求相关类统一管理，提高了代码的可维护性和复用性。
- *(rocketmq)* 重构阿里云RocketMQ客户端配置 • 将RocketMQAliyunClient重命名为AliyunRocketMQClient • 修改配置属性前缀从"rocketmq"改为"aliyun-rocketmq" • 优化消息查询接口直接返回消息体内容 test(rocketmq): 更新测试用例适配客户端变更 • 修改测试类名和注入的客户端类型 • 简化MQ消息验证逻辑直接解析消息体 • 移除冗余的消息topic和tag校验 fix(task): 修正任务重试状态校验逻辑 • 修复任务重试时错误的状态检查条件 • 确保只有已完成的任务才能被重试 feat(node): 新增任务节点事件监听器 • 实现任务完成时自动触发节点状态更新 • 通过调度器提交节点工作单元 • 完善节点与任务的联动处理 chore(deps): 添加aigc-image-sdk依赖 • 引入美图AI能力SDK支持透明图处理 • 更新gradle构建配置 style(config): 优化调度器配置格式 • 调整代码缩进和参数换行保持一致性 • 补充节点元数据监听器注册逻辑 本次提交主要包含配置重构、功能优化和缺陷修复，涉及消息队列客户端、任务调度和节点处理等核心模块的改进。
- *(client)* 优化请求参数解析器实现 • 使用Jackson替代手动属性映射，简化对象到请求参数的转换 • 移除冗余的时间格式化逻辑和反射代码 • 提取公共方法并添加详细注释提升可读性
- *(scheduler)* 重构任务调度器核心接口与实现 • 引入Payload接口统一任务负载标识 • 简化Work接口，移除workId属性 • 重构BackoffScheduler及其实现类，优化任务提交逻辑 主要变更： - Executor接口泛型参数从Work改为Payload - Scheduler接口明确区分Payload和Work类型 - 移除冗余的submitWork方法，统一使用submit - 测试用例同步更新以适应新接口
- *(rocketmq)* 重构消息发送逻辑并优化测试用例 • 简化RocketMQClient的send方法，直接使用Message类构建消息 • 将消息构建逻辑从ApiTaskListener移到RocketMQClient中 • 更新测试用例以验证重构后的消息发送功能 本次重构主要优化了消息发送的封装性，使客户端代码更简洁。测试用例现在使用随机key验证完整流程。
- *(ksp-service)* 移除枚举注释解析功能及相关代码 • 删除EnumCommentParser及其相关工具类 • 简化TaskNodeController和TaskHandlerRestController中的枚举处理逻辑 • 不再解析和显示枚举注释，改为直接引用枚举值
- *(scheduler)* 替换虚拟线程调度器为IO调度器 • 将Executors.newVirtualThreadPerTaskExecutor()替换为Dispatchers.IO
- *(redis-lock)* 简化分布式锁实现并优化调度器逻辑 • 将executeWithLock重命名为runWithLock并移除超时参数 • 将锁处理逻辑移至BackoffScheduler基类 • 移除冗余的CannotAcquireLockException及相关引用 测试用例展示了数据库死锁场景，但未包含在正式修改中
- *(scheduler)* 重构调度器接口与实现 • 将BackoffScheduler改为实现Scheduler接口，增强扩展性 • 统一调度器方法签名，移除冗余的批量提交方法 • 添加processWork方法到接口，支持直接处理工作 涉及多个监听器和控制器的调度器类型修改，保持接口一致性
- *(scheduler)* 重构调度器模块实现 • 删除旧的Backoff相关类，重新设计调度器接口 • 实现基于延迟队列的新调度器架构 • 简化配置结构，优化退避策略实现 主要变更： - 移除Backoff、BackoffWork等旧实现 - 新增DelayedQueue接口和Redisson实现 - 重构调度器核心逻辑为DelayedQueueScheduler - 简化配置为ExponentialBackoffConfig - 优化退避策略为独立的BackoffPolicy接口 该重构使调度器设计更清晰，解耦了队列实现与核心逻辑，提升了可维护性。
- *(scheduler)* 优化延迟队列调度器实现 • 重构 DelayedQueueScheduler 的入队逻辑，合并 DelayedTask 创建与延迟计算 • 移除未使用的 Duration 导入 • 简化日志记录中时间计算逻辑 test(backoff): 添加指数退避策略测试用例 • 新增 ExponentialBackoffPolicyTest 验证基础2的退避算法 • 测试包含1-20次重试的延迟时间输出 fix(node-executor): 分离工作流与节点状态检查 • 将 NodeExecutor 中的状态检查拆分为独立步骤 • 优化日志信息以区分工作流终止和节点不可执行情况 chore(test): 重新启用工作流执行测试 • 移除 GraphEngineTest 中 @Disabled 注解 • 恢复工作流执行测试用例
- *(event-handling)* 优化事件监听机制并移除随机延迟 • 将TransactionalEventListener替换为EventListener，简化事务处理逻辑 • 在EventPublisher中添加事务隔离确保事件可靠发布 • 移除调度器中的随机延迟，使用固定退避策略 本次修改主要解决事件监听器在事务中的执行问题，并优化调度器的稳定性。EventPublisher现在会在新事务中发布事件，确保与主事务隔离。同时移除了不必要的随机延迟，使调度行为更可预测。
- *(youchuan/diffusion)* 重构悠船衍生任务节点和处理器 • 将YouChuanDiffusionTaskNode重命名为YouChuanModelImageDerivationTaskNode以明确用途 • 拆分基础处理器为YouChuanDiffusionTaskHandler并新增两个子类处理不同衍生类型 • 新增YouChuanImageDerivationTaskNode用于处理图案衍生任务 涉及文件重命名、类继承结构调整和新增图案衍生功能模块
- *(task-controller)* 优化任务分组状态聚合逻辑 • 新增状态聚合规则：RUNNING 优先，全 FAILED/CANCELLED 返回对应状态，Finished 状态特殊处理 • 添加分组状态筛选功能，支持按指定状态过滤任务列表
- *(task-management)* 重构任务存储机制 • 将任务关联数据从节点上下文迁移至任务详情表 • 新增任务重试功能并统一处理逻辑 • 修改所有任务节点使用新的存储方式 • 移除节点上下文中的存储相关方法
- *(rocketmq)* 移除RocketMQ消费者相关代码 移除了RocketMQClient中的SimpleConsumer依赖及相关配置，简化了RocketMQ客户端结构。 将RocketMQConfiguration包路径调整为正确的config目录下。 docs(db): 添加数据库初始化SQL脚本 新增了数据库表结构定义、外键约束以及删除脚本，包含： - 工作流定义相关表 - 节点实例相关表 - 任务实例相关表 - 工作流实例相关表
- *(rocketmq)* 优化阿里云RocketMQ客户端消息查询逻辑 修改`getMessageByKey`方法返回类型为可空String，当查询不到消息时返回null 调整测试用例中的等待逻辑，改为直接检查MQ消息是否存在 缩短测试用例中的最大等待时间从35秒减少到30秒 测试用例不再依赖数据库状态检查，改为通过MQ消息确认任务完成状态
- *(lazada)* 重构Lazada客户端代码，提取HTTP请求逻辑到独立接口 将原有的直接使用RestClient的代码重构为使用新引入的LazadaHttpClient接口，主要变更包括： 1. 移除RestClient相关依赖和配置代码 2. 新增LazadaHttpClient接口定义所有API调用 3. 将签名生成和公共参数处理逻辑保留在LazadaClient中 4. 简化各业务方法实现，统一响应处理逻辑 BREAKING CHANGE: 需要注入LazadaHttpClient实例来构造LazadaClient
- *(controller)* 替换check为BusinessException并优化参数命名 将NodeInstanceController中的check校验替换为抛出BusinessException， 提高错误信息的业务可读性。同时修改ConsoleController的 resubmitRunningTasks方法参数名，使其更符合业务语义。
- *(scheduler)* 重构分布式锁逻辑并移除Redissons工具类 将分布式锁的实现从Redissons工具类迁移至DelayedQueueScheduler内部，新增trigger方法作为调度器接口的统一触发入口。同时修改ConsoleController中相关方法名及调用逻辑，使其语义更清晰。 此次重构优化了代码组织结构，使锁控制逻辑更贴近业务场景，同时消除了不必要的工具类依赖。
- *(task)* 重构TaskHandler为接口并优化相关实现 将TaskHandler从抽象类改为接口，并调整所有实现类继承方式。主要变更包括： 1. 修改TaskHandler为接口，方法改为默认实现 2. 移除实现类中多余的括号 3. 优化YouChuanDiffusionTaskHandler结构，使用委托模式 4. 改进TaskHandlerRegistry中处理器过滤逻辑 这些变更有助于提高代码的灵活性和可维护性，为后续扩展提供更好的支持
- *(node)* 改进错误信息中的数据类型转换日志 在 NodeExecutor 中将数据类型转换失败的错误信息修改为包含原始数据的 JSON 格式，使其更易于调试。
- *(config)* 移动RocketMQProperties到config包并清理无用导入 将RocketMQProperties类从infra.rocketmq包移动到config包，使其更符合Spring配置类的存放规范。同时移除了RocketMQConfiguration中未使用的RocketMQProperties导入语句。 这些改动属于代码结构调整，不涉及功能变更。
- *(workflow)* 将节点中的workflowInstanceId统一更名为workflowId 修改了多个文件中的节点相关代码，将所有引用workflowInstanceId的地方统一更名为workflowId，保持命名一致性。这包括控制器、事件发布器、工作流引擎、节点执行器等组件中对工作流实例ID的引用。 此次修改涉及工作流核心逻辑，但不会改变现有功能行为，仅进行命名规范化。
- *(backoff)* 移除ExponentialBackoffConfig中的maxTimes参数 移除ExponentialBackoffConfig类中的maxTimes字段，该字段不再需要。 同时更新了相关测试用例和默认配置，以反映这一变更。 测试用例新增了defaultConfiguration测试方法，验证默认配置下的退避行为。

### 📚 Documentation

- 统一多个AI任务节点的输入输出参数注释 • 为PushPodStyleNode、KontextPodTaskNode等节点添加@NodeProperties标注 • 标准化参数名称如"款式图"、"模特图"等 • 为输出参数添加描述性注释和标注
- *(workflow)* 为工作流定义控制器和方法添加文档注释 为 WorkflowDefinitionController 中的 create 方法添加了详细的 KDoc 注释，说明其功能和参数。
- *(core)* 更新Ability枚举的注释描述 修改了Ability.kt文件中TEXTURE和TRY_ON两个枚举项的注释说明： - 将"贴图"改为更准确的"图套贴图" - 将"虚拟换衣"改为更清晰的"服装上身" 这些修改使枚举值的业务含义更加明确

### ⚡ Performance

- *(engine)* 简化工作流失败检查逻辑 • 移除实时构建图状态的冗余检查 • 修改为任一节点失败即直接终止工作流

### 🎨 Styling

- *(murmuration-service)* 移除 MidjourneyDiffusionTaskNode 中的 override 关键字
- *(scheduler)* 调整调度器方法修饰符及代码组织样式
- 迁移风格管理模块的实体和DTO路径
- 统一代码格式和注释规范 - IDEA 代码检查修复

### 🧪 Testing

- *(scheduler)* 任务管理器集成测试 - 修改 TaskExecutor 构造函数，注入 EventPublisher - 优化任务完成、失败和超时的事件发布逻辑 - 调整 RedisBackoffScheduler 初始化逻辑 - 重构 EventPublisher为 Spring 组件 - 添加 TaskManager 测试用例
- *(task-manager)* 优化 task-manager 测试用例 - 使用随机生成的 nodeInstanceId 替代硬编码值 - 简化任务创建和查询逻辑 - 调整等待时间和轮询间隔 - 优化结果验证逻辑，确保输出不为空
- 调整测试用例的超时和轮询参数 • 修改AipExecutorTest中的await参数，将超时延长至5-6分钟，轮询间隔调整为20秒 • 在KontextPod测试用例中添加@Disabled注解 • 恢复TaskManagerTest中kontextPodTest的await逻辑并调整参数
- *(upscale-task)* 添加超分任务客户端测试用例 • 实现创建超分任务的完整测试流程 • 验证任务状态变更及MQ消息通知 • 包含数据库查询和异步等待逻辑
- *(workflow)* 更新节点执行集成测试用例
- *(workflow-integration)* 更新节点执行集成测试并调整参数默认值 • 移除NodeExecutionIntegrationTest中的@Disabled注解以启用测试 • 修改工作流定义ID为固定值7344269212250353665 • 在YouChuanDiffusionTaskNode中将moodboardId参数设为可空并添加默认值null 。
- *(workflow-expression)* 修改测试用例中的上下文初始化
- *(task)* 调整测试任务超时时间至35秒 • 将TaskManagerTest、UpScaleTaskControllerTest和UpScaleTaskClientTest中的await超时从30秒延长至35秒 • 禁用RedisBackoffSchedulerIntegrationTest测试类（添加@Disabled注解） • 提升测试稳定性，适应任务处理耗时波动
- *(graph-engine)* 添加多个工作流定义测试用例 • 新增4种图案开款工作流定义测试方法 • 完善工作流节点定义和边连接逻辑 • 清理测试代码中的注释和冗余内容
- *(graph-engine)* 禁用工作流执行测试用例
- 禁用开发阶段使用的测试类 在多个AI服务相关的测试类中添加了@Disabled("开发阶段使用")注解

### ⚙️ Miscellaneous Tasks

- 更新 Gradle 分发 URL 并添加 CI 模板 - 修改 Gradle 分发 URL为腾讯云镜像源 - 添加 GitLab CI 模板，使用 aikero/devops项目中的配置 - 更新 Gradle 版本并添加 foojay-resolver 插件 - 在 murmuration-sdk 模块中添加 API 版本生成器插件和 Spring Cloud依赖
- 移除废弃的Midjourney相关文件和代码 feat: 重构任务处理机制 - 新增TaskHandler抽象类和TaskIdentifier注解 - 完善任务管理相关类(TaskManager/TaskExecutor) - 重构任务实体类结构 refactor: 重命名AiTask为TaskInstance及相关实体类 fix(core): 修改TaskStatus枚举值名称 - 将PREPARE改为PREPARED
- *(build)* 移除本地jar依赖改用maven仓库 - 删除本地libs目录下的lazop-api-sdk jar文件 - 修改build.gradle.kts使用maven依赖替代 - 更新LazadaClientTest测试方法命名
- *(config)* 添加本地环境配置文件支持
- *(config)* 修改应用名称配置

📝 Generated by <strong>aikero-ci-robot</strong> with ❤️
