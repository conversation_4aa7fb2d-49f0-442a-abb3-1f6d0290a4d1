export team.aikero.murmuration.core.workflow.entity.WorkflowDefinition
    -> package team.aikero.murmuration.core.workflow.dto

specification WorkflowDefinitionPageReq {
    category
}

WorkflowDefinitionPageVo {
    id
    name
    category
    nodeDefinitions {
        #allScalars
        flat(nodeMetadata){
            supplier
            ability
            type
        }
    }
    edges {
        #allScalars
        -id
    }
}
