package team.aikero.murmuration.core.workflow.entity

import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.Key
import org.babyfish.jimmer.sql.Serialized
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.LongId
import team.aikero.blade.data.jimmer.entity.RevisedTime
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.workflow.NodeType
import java.io.Serializable

/**
 * 节点元数据
 *
 * 存储代码中定义的节点信息，供“画布”选择的节点信息
 *
 * <AUTHOR>
 */
@Entity
interface NodeMetadata: LongId, CreatedTime, RevisedTime {

    /**
     * 节点名称
     */
    val name: String

    /**
     * 节点类型
     */
    val type: NodeType

    /**
     * 供应商
     *
     * 提供能力的来源，提供者
     */
    @Key
    val supplier: Supplier

    /**
     * 能力
     *
     * 业务上定义的 AI 功能或能力类型
     */
    @Key
    val ability: Ability

    /**
     * 节点输入参数
     */
    @Serialized
    @Default("[]")
    val input: List<Variable>

    /**
     * 节点参数
     */
    @Serialized
    @Default("[]")
    val parameter: List<Variable>

    /**
     * 节点输出参数
     */
    @Serialized
    @Default("[]")
    val output: List<Variable>

}

/**
 * 节点变量
 *
 * <AUTHOR>
 */
data class Variable (

    /**
     * 变量组
     */
    val group: VariableGroup,

    /**
     * 名称
     */
    val name: String,

    /**
     * 类型
     */
    val type: String,

    /**
     * 是否必填
     */
    val required: Boolean,

    /**
     * 描述
     */
    val description: String?,

    /**
     * 值
     */
    val value: String?,

    /**
     * 最小值
     */
    val min: Int?,

    /**
     * 最大值
     */
    val max: Int?,
): Serializable

/**
 * 变量类型
 */
enum class VariableGroup {
    /**
     * 输入
     */
    INPUT,

    /**
     * 参数
     */
    PARAMETER,

    /**
     * 输出
     */
    OUTPUT
}
