package team.aikero.murmuration.core.workflow.node.builtin

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.transaction.annotation.Transactional
import team.aikero.murmuration.core.workflow.NodeType
import team.aikero.murmuration.core.workflow.WorkflowNode
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.entity.NodeStatus
import team.aikero.murmuration.core.workflow.node.NodeResult
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.task.TaskManager
import team.aikero.murmuration.core.workflow.task.TaskResult

/**
 * 任务节点基础接口
 *
 * <AUTHOR>
 */
abstract class TaskNode<I : Input, P : Parameter, O : Output> : WorkflowNode<I, P, O> {
    override val type = NodeType.TASK

    @Autowired
    lateinit var taskManager: TaskManager

    /**
     * 节点执行
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun execute(context: WorkflowNodeContext, input: I, parameter: P): NodeResult<O> {
        return when (context.node.status) {
            NodeStatus.READY -> {
                // 创建任务
                createTask(context, input, parameter)
                NodeResult.Running
            }

            NodeStatus.RUNNING -> {
                // 查询任务
                val taskResult = taskManager.queryTaskStatus(context.node.id)
                when (taskResult) {
                    is TaskResult.Running -> NodeResult.Running
                    is TaskResult.Completed -> {
                        val needReview = context.node.nodeDefinition.review
                        if (needReview) {
                            NodeResult.Reviewing
                        } else {
                            // 无需验收时，将所有任务结果的验收状态更新为“通过”
                            taskManager.updateAllTaskResultPassed(context.node.id)
                            val output = assembleNodeOutput(context)
                            NodeResult.Completed(output)
                        }
                    }

                    is TaskResult.Failed -> NodeResult.Failed(taskResult.error)
                }
            }

            NodeStatus.REVIEWED -> {
                val output = assembleNodeOutput(context)
                NodeResult.Completed(output)
            }

            // 不会到这里
            NodeStatus.PREPARE,
            NodeStatus.REVIEWING,
            NodeStatus.COMPLETED,
            NodeStatus.FAILED -> NodeResult.Failed("非法的节点状态[${context.node.status}]")
        }
    }

    /**
     * 创建任务
     */
    abstract fun createTask(context: WorkflowNodeContext, input: I, parameter: P)

    /**
     * 将任务结果组装成节点输出
     */
    abstract fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): O

    /**
     * 组装节点输出
     */
    private fun assembleNodeOutput(context: WorkflowNodeContext): O {
        // 查询任务(过滤审核通过的结果)
        val taskInstances = taskManager.queryTaskWithPassedResult(context.node.id)

        val taskResults = taskInstances
            .map { task ->
                task.results.map {
                    SimpleTaskResult(
                        taskId = task.id,
                        url = it.url,
                    )
                }
            }
            .flatten()
        return assembleOutput(context, taskResults)
    }
}

/**
 * 任务结果
 */
data class SimpleTaskResult(
    /**
     * 所属任务ID
     */
    val taskId: Long,

    /**
     * 图片URL
     */
    val url: String,
)
