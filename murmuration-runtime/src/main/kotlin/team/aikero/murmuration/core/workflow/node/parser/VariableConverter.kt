package team.aikero.murmuration.core.workflow.node.parser

import org.slf4j.LoggerFactory
import team.aikero.murmuration.core.workflow.entity.Variable
import team.aikero.murmuration.core.workflow.entity.VariableGroup

/**
 * 变量转换器
 *
 * 负责将解析后的字段信息转换为 Variable 对象
 *
 * <AUTHOR>
 */
object VariableConverter {

    private val logger = LoggerFactory.getLogger(VariableConverter::class.java)

    /**
     * 将字段信息列表转换为 Variable 列表
     *
     * @param fieldInfos 字段信息列表
     * @param group 变量组类型
     * @return Variable 列表
     */
    fun convertToVariables(fieldInfos: List<FieldInfo>, group: VariableGroup): List<Variable> {
        return fieldInfos.mapNotNull { fieldInfo ->
            try {
                convertFieldToVariable(fieldInfo, group)
            } catch (e: Exception) {
                logger.warn("转换字段 ${fieldInfo.name} 为 Variable 时发生错误", e)
                null
            }
        }
    }

    /**
     * 将单个字段信息转换为 Variable
     */
    private fun convertFieldToVariable(fieldInfo: FieldInfo, group: VariableGroup): Variable {
        return Variable(
            group = group,
            name = fieldInfo.name,
            type = fieldInfo.parsedType.name,
            required = fieldInfo.required,
            description = generateDescription(fieldInfo),
            value = fieldInfo.defaultValue,
            min = fieldInfo.minValue,
            max = fieldInfo.maxValue
        )
    }

    /**
     * 生成字段描述
     */
    private fun generateDescription(fieldInfo: FieldInfo): String? {
        val typeDescription = generateTypeDescription(fieldInfo.parsedType)
        val constraintDescription = generateConstraintDescription(fieldInfo)

        return listOfNotNull(fieldInfo.description, typeDescription, constraintDescription)
            .filter { it.isNotBlank() }
            .joinToString("; ")
            .takeIf { it.isNotBlank() }
    }

    /**
     * 生成类型描述
     */
    private fun generateTypeDescription(type: ParsedType): String? {
        return when {
            type.isArray -> "数组类型"
            type.isGeneric && type.typeArguments.isNotEmpty() -> {
                when (type.simpleName.lowercase()) {
                    "list" -> "列表"
                    "set" -> "集合"
                    "map" -> "映射"
                    else -> "泛型类型"
                }
            }

            else -> when (type.simpleName.lowercase()) {
                "string" -> "字符串"
                "int", "integer" -> "整数"
                "long" -> "长整数"
                "double" -> "双精度浮点数"
                "float" -> "单精度浮点数"
                "boolean" -> "布尔值"
                "date", "localdatetime", "localdate" -> "日期时间"
                else -> null
            }
        }
    }

    /**
     * 生成约束描述
     */
    private fun generateConstraintDescription(fieldInfo: FieldInfo): String? {
        val constraints = mutableListOf<String>()

        if (fieldInfo.required) {
            constraints.add("必填")
        }

        if (fieldInfo.minValue != null && fieldInfo.maxValue != null) {
            constraints.add("范围: ${fieldInfo.minValue}-${fieldInfo.maxValue}")
        } else if (fieldInfo.minValue != null) {
            constraints.add("最小值: ${fieldInfo.minValue}")
        } else if (fieldInfo.maxValue != null) {
            constraints.add("最大值: ${fieldInfo.maxValue}")
        }

        // 从注解中提取其他约束信息
        fieldInfo.annotations.forEach { (annotationName, _) ->
            when (annotationName) {
                "NotBlank" -> if (!constraints.contains("必填")) constraints.add("不能为空白")
                "NotEmpty" -> if (!constraints.contains("必填")) constraints.add("不能为空")
                "Email" -> constraints.add("邮箱格式")
                "Pattern" -> constraints.add("格式验证")
                "Positive" -> constraints.add("正数")
                "Negative" -> constraints.add("负数")
                "Future" -> constraints.add("未来时间")
                "Past" -> constraints.add("过去时间")
            }
        }

        // 提取枚举值
        if (fieldInfo.parsedType.type is Class<*> && fieldInfo.parsedType.type.isEnum) {
            val enumValues = fieldInfo.parsedType.type.enumConstants.map { it.toString() }
            constraints.add("可选值: [${enumValues.joinToString(", ")}]")
        }

        return constraints.takeIf { it.isNotEmpty() }?.joinToString(", ")
    }

    /**
     * 处理复杂类型的嵌套变量
     *
     * 对于复杂的嵌套类型，可能需要展开为多个变量
     */
    fun expandComplexType(fieldInfo: FieldInfo, group: VariableGroup, prefix: String = ""): List<Variable> {
        val variables = mutableListOf<Variable>()

        // 添加主变量
        variables.add(convertFieldToVariable(fieldInfo, group))

        // 如果是复杂类型，尝试展开
        if (shouldExpandType(fieldInfo.parsedType)) {
            variables.addAll(expandTypeArguments(fieldInfo, group, prefix))
        }

        return variables
    }

    /**
     * 判断是否需要展开类型
     */
    private fun shouldExpandType(type: ParsedType): Boolean {
        return type.isGeneric && type.typeArguments.isNotEmpty() &&
                type.simpleName.lowercase() in listOf("map", "pair")
    }

    /**
     * 展开类型参数
     */
    private fun expandTypeArguments(fieldInfo: FieldInfo, group: VariableGroup, prefix: String): List<Variable> {
        val variables = mutableListOf<Variable>()
        val type = fieldInfo.parsedType

        if (type.simpleName.lowercase() == "map" && type.typeArguments.size >= 2) {
            // 对于 Map<K, V>，创建 key 和 value 的变量描述
            val keyType = type.typeArguments[0]
            val valueType = type.typeArguments[1]

            variables.add(
                Variable(
                    group = group,
                    name = "${prefix}${fieldInfo.name}.key",
                    type = keyType.name,
                    required = false,
                    description = "映射的键类型",
                    value = null,
                    min = null,
                    max = null
                )
            )

            variables.add(
                Variable(
                    group = group,
                    name = "${prefix}${fieldInfo.name}.value",
                    type = valueType.name,
                    required = false,
                    description = "映射的值类型",
                    value = null,
                    min = null,
                    max = null
                )
            )
        }

        return variables
    }
}
