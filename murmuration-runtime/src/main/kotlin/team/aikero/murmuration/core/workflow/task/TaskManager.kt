package team.aikero.murmuration.core.workflow.task

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.treeToValue
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.asNonNull
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.EventPublisher
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.workflow.entity.*
import team.aikero.murmuration.core.workflow.event.TaskPrepareEvent
import team.aikero.murmuration.core.workflow.event.TaskWork
import team.aikero.murmuration.core.workflow.task.TaskResult.InternalErrorType.ALL_TASK_FAILED

/**
 * 任务管理器
 *
 * <AUTHOR>
 */
@Component("murmurationTaskManager")
class TaskManager(val sql: KSqlClient, val objectMapper: ObjectMapper, val eventPublisher: EventPublisher) {

    /**
     * 发起任务(从工作流发起)
     */
    @Transactional(rollbackFor = [Exception::class])
    fun createTask(
        nodeInstanceId: Long,
        supplier: Supplier,
        ability: Ability,
        request: Any,
        storage: Any? = null,
        groupIndex: Long? = null,
    ): Long {
        // 持久化任务实例
        val id = IdHelper.getId()
        val taskInstance = sql.insert(TaskInstance {
            this.id = id
            this.triggerSource = TriggerSource.WORKFLOW
            this.nodeInstance = NodeInstance {
                this.id = nodeInstanceId
            }
            this.supplier = supplier
            this.ability = ability
            this.status = TaskStatus.PREPARED
            this.groupIndex = groupIndex ?: id
            this.detail = TaskInstanceDetail {
                this.request = objectMapper.valueToTree(request)
                this.storage = storage?.let { objectMapper.valueToTree(it) }
            }
        }).modifiedEntity

        // 发布事件
        val taskWork = TaskWork(taskInstance)
        eventPublisher.publish(TaskPrepareEvent(taskWork))

        return taskInstance.id
    }

    /**
     * 重试任务
     */
    @Transactional(rollbackFor = [Exception::class])
    fun retryTask(taskId: Long) {
        val taskInstance = sql.findOneById(newFetcher(TaskInstance::class).by {
            nodeInstance()
            supplier()
            ability()
            status()
            detail {
                request()
                storage()
            }
            groupIndex()
        }, taskId)

        if (!taskInstance.status.isFinished()) {
            throw BusinessException("任务[${taskInstance.id}]当前状态[${taskInstance.status}]，无法重试")
        }

        // 基于任务信息重新创建一个任务
        this.createTask(
            nodeInstanceId = taskInstance.nodeInstance!!.id,
            supplier = taskInstance.supplier,
            ability = taskInstance.ability,
            request = taskInstance.detail!!.request,
            storage = taskInstance.detail!!.storage,
            groupIndex = taskInstance.groupIndex,
        )
    }

    /**
     * 发起任务(从API调用)
     */
    @Transactional(rollbackFor = [Exception::class])
    fun createTask(
        bizId: String,
        bizType: String,
        supplier: Supplier,
        ability: Ability,
        request: Any,
    ): Long {
        // 持久化任务实例
        val id = IdHelper.getId()
        val taskInstance = sql.insert(TaskInstance {
            this.id = id
            this.triggerSource = TriggerSource.API
            this.bizId = bizId
            this.bizType = bizType
            this.supplier = supplier
            this.ability = ability
            this.status = TaskStatus.PREPARED
            this.groupIndex = id
            this.detail = TaskInstanceDetail {
                this.request = objectMapper.valueToTree(request)
            }
        }).modifiedEntity

        // 发布事件
        eventPublisher.publish(
            TaskPrepareEvent(
                TaskWork(
                    id = taskInstance.id,
                    supplier = supplier,
                    ability = ability,
                )
            )
        )

        return taskInstance.id
    }

    /**
     * 创建一个已完成的任务
     */
    @Transactional(rollbackFor = [Exception::class])
    fun createCompletedTask(
        nodeInstanceId: Long,
        supplier: Supplier,
        ability: Ability,
        groupIndex: Int? = null,
        results: List<TaskInstanceResult>,
    ): Long {
        // 持久化任务实例
        val id = IdHelper.getId()
        val taskInstance = sql.insert(TaskInstance {
            this.id = id
            this.triggerSource = TriggerSource.WORKFLOW
            this.nodeInstanceId = nodeInstanceId
            this.supplier = supplier
            this.ability = ability
            this.status = TaskStatus.COMPLETED
            this.groupIndex = groupIndex?.toLong() ?: id
            this.results = results
        }).modifiedEntity

        return taskInstance.id
    }

    /**
     * 查询任务状态(从工作流发起)
     */
    fun queryTaskStatus(nodeInstanceId: Long): TaskResult<Unit> {
        val taskInstances = sql.executeQuery(TaskInstance::class) {
            where(table.nodeInstanceId eq nodeInstanceId)
            select(table.fetchBy {
                status()
            })
        }

        return toAsyncResult(taskInstances)
    }

    /**
     * 查询任务状态(从API调用)
     */
    fun queryTaskStatus(bizId: String, bizType: String): TaskResult<Unit> {
        val taskInstances = sql.executeQuery(TaskInstance::class) {
            where(table.bizId eq bizId)
            where(table.bizType eq bizType)
            select(table.fetchBy {
                status()
            })
        }

        return toAsyncResult(taskInstances)
    }

    private fun toAsyncResult(taskInstances: List<TaskInstance>): TaskResult<Unit> {
        if (taskInstances.isEmpty()) {
            return TaskResult.Completed(Unit)
        }

        // 所有任务失败，则节点失败
        val allFailed = taskInstances.all { it.status == TaskStatus.FAILED }
        if (allFailed) {
            return TaskResult.Failed(ALL_TASK_FAILED)
        }

        // 所有任务完成(包括成功/失败/取消)
        val allFinished = taskInstances.all { it.status.isFinished() }
        if (allFinished) {
            return TaskResult.Completed(Unit)
        }

        // 存在未完成节点
        return TaskResult.Running
    }

    /**
     * 查询任务(过滤审核通过的结果)
     */
    fun queryTaskWithPassedResult(nodeInstanceId: Long): List<TaskInstance> {
        return sql.executeQuery(TaskInstance::class) {
            where(table.nodeInstanceId eq nodeInstanceId)
            select(table.fetchBy {
                results({
                    filter {
                        where(table.passed eq true)
                    }
                }) {
                    url()
                }
            })
        }
    }

    /**
     * 将所有任务结果的验收状态更新为“通过”
     */
    fun updateAllTaskResultPassed(nodeInstanceId: Long) {
        sql.executeUpdate(TaskInstanceResult::class) {
            where(table.task.nodeInstanceId eq nodeInstanceId)
            set(table.passed, true)
        }
    }
}

/**
 * 读取任务存储
 */
inline fun <reified T> TaskManager.readTaskStorage(nodeInstanceId: Long): Map<Long, T> {
    return sql
        .executeQuery(TaskInstanceDetail::class) {
            where(table.task.nodeInstanceId eq nodeInstanceId)
            select(table.taskId.asNonNull(), table.storage.asNonNull())
        }
        .associate {
            val taskId = it._1
            val storage = objectMapper.treeToValue<T>(it._2)
            Pair(taskId, storage)
        }
}
