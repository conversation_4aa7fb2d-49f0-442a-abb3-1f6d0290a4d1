package team.aikero.murmuration.core.workflow.engine

import org.babyfish.jimmer.sql.DissociateAction
import org.babyfish.jimmer.sql.ast.mutation.AssociatedSaveMode
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.code.entity.DefaultSimpleCodeRule
import team.aikero.blade.sequence.code.generate.BusinessCodeGenerator
import team.aikero.murmuration.controller.web.NodeDefinitionArgs
import team.aikero.murmuration.core.EventPublisher
import team.aikero.murmuration.core.workflow.NodeType
import team.aikero.murmuration.core.workflow.entity.*
import team.aikero.murmuration.core.workflow.entity.WorkflowMode.WORKFLOW
import team.aikero.murmuration.core.workflow.event.NodeCompletedEvent
import team.aikero.murmuration.core.workflow.event.NodeFailedEvent
import team.aikero.murmuration.core.workflow.event.NodeReadyEvent
import team.aikero.murmuration.core.workflow.event.NodeWork.Companion.toWork
import team.aikero.murmuration.core.workflow.event.WorkflowCancelledEvent
import team.aikero.murmuration.core.workflow.event.WorkflowCompletedEvent
import team.aikero.murmuration.core.workflow.event.WorkflowFailedEvent
import team.aikero.murmuration.core.workflow.event.WorkflowRunningEvent
import team.aikero.murmuration.core.workflow.event.WorkflowStartEvent
import team.aikero.murmuration.core.workflow.event.WorkflowWaitingEvent
import team.aikero.murmuration.core.workflow.event.WorkflowWork.Companion.toWork

/**
 * 图执行引擎
 *
 * 负责工作流的图结构解析和执行调度:
 * - 解析WorkflowDefinition的图结构
 * - 计算节点依赖关系和执行顺序
 * - 管理工作流实例的生命周期
 *
 * <AUTHOR>
 */
@Component
class DatabaseGraphEngine(
    private val sqlClient: KSqlClient,
    private val eventPublisher: EventPublisher,
    private val businessCodeGenerator: BusinessCodeGenerator,
) : GraphEngine {

    /**
     * 工作流实例编码规则
     *
     * 如: WF-202501010001
     */
    private val codeRule = DefaultSimpleCodeRule("WF", "MURMURATION", "yyMMdd", "%1$04d")

    /**
     * 启动工作流
     *
     * @param workflowDefinitionId 工作流定义ID
     * @param serialNumber 流水号
     * @param input 输入参数
     * @return 工作流实例ID
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun startWorkflow(
        workflowDefinitionId: Long,
        input: Map<String, Any>,
        parameter: Map<String, Any>,
        nodeArgs: List<NodeDefinitionArgs>
    ): Long {
        // 生成唯一的流水号
        val serialNumber = businessCodeGenerator.generate(codeRule)

        log.info { "启动工作流, 定义ID: $workflowDefinitionId, 流水号: $serialNumber" }

        // 1. 创建工作流实例
        val workflowInstanceId = createWorkflowInstance(workflowDefinitionId, serialNumber, input, parameter, nodeArgs)

        // 2. 加载工作流定义和创建节点实例
        val workflowDefinition = loadWorkflowDefinition(workflowDefinitionId)
        createNodeInstancesForWorkflow(workflowDefinition, workflowInstanceId, input, nodeArgs)

        // 3. 发布工作流启动事件
        publishWorkflowStartEvent(workflowInstanceId)

        // 4. 计算并执行第一批就绪节点
        scheduleReadyNodes(workflowInstanceId)

        return workflowInstanceId
    }

    /**
     * 获取工作流状态
     */
    override fun getWorkflowStatus(workflowInstanceId: Long): WorkflowStatusInfo {
        val workflowInstance = loadWorkflowInstance(workflowInstanceId)
        val nodeInstances = loadNodeInstances(workflowInstanceId)

        val nodeStatusList = nodeInstances.map { node ->
            NodeStatusInfo(
                nodeInstanceId = node.id,
                nodeKey = node.nodeDefinition.nodeKey,
                status = node.status,
                supplier = node.supplier,
                ability = node.ability,
                startTime = node.createdTime,
                endTime = node.revisedTime,
                errorMessage = node.detail?.error
            )
        }

        return WorkflowStatusInfo(
            workflowInstanceId = workflowInstance.id,
            serialNumber = workflowInstance.serialNumber,
            status = workflowInstance.status,
            totalNodes = nodeInstances.size,
            completedNodes = nodeInstances.count { it.status == NodeStatus.COMPLETED },
            failedNodes = nodeInstances.count { it.status == NodeStatus.FAILED },
            runningNodes = nodeInstances.count { it.status == NodeStatus.RUNNING },
            readyNodes = nodeInstances.count { it.status == NodeStatus.READY },
            startTime = workflowInstance.createdTime,
            endTime = workflowInstance.revisedTime,
            nodeStatusList = nodeStatusList
        )
    }

    /**
     * 取消工作流
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun cancelWorkflow(workflowInstanceId: Long) {
        log.info { "取消工作流: $workflowInstanceId" }

        updateWorkflowStatus(workflowInstanceId, WorkflowStatus.CANCELLED)

        eventPublisher.publish(WorkflowCancelledEvent(loadWorkflowInstance(workflowInstanceId).toWork()))
    }

    /**
     * 重试失败的工作流
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun retryWorkflow(workflowInstanceId: Long) {
        log.info { "重试工作流: $workflowInstanceId" }

        val workflowInstance = loadWorkflowInstance(workflowInstanceId)
        if (workflowInstance.status != WorkflowStatus.FAILED) {
            throw IllegalStateException("只能重试失败的工作流")
        }

        // 重置失败的节点
        resetFailedNodes(workflowInstanceId)

        // 重新启动工作流
        updateWorkflowStatus(workflowInstanceId, WorkflowStatus.RUNNING)

        eventPublisher.publish(WorkflowRunningEvent(loadWorkflowInstance(workflowInstanceId).toWork()))

        // 重新计算就绪节点
        scheduleReadyNodes(workflowInstanceId)
    }

    /**
     * 重置失败的节点
     */
    private fun resetFailedNodes(workflowInstanceId: Long) {
        // 抓取失败节点的所有关联数据
        val failedNodeInstances = sqlClient.executeQuery(NodeInstance::class) {
            where(table.workflowInstance.id eq workflowInstanceId)
            where(table.status eq NodeStatus.FAILED)
            select(table.fetchBy {
                allTableFields()
                detail {
                    allScalarFields()
                }
                taskInstances {
                    allScalarFields()
                    detail {
                        allScalarFields()
                    }
                    results {
                        allScalarFields()
                    }
                }
            })
        }

        // 构建新的节点实例
        val newNodeInstances = failedNodeInstances.map { oldNode ->
            NodeInstance {
                this.id = oldNode.id
                this.nodeDefinitionId = oldNode.nodeDefinition.id
                this.workflowInstanceId = oldNode.workflowInstance?.id
                this.supplier = oldNode.supplier
                this.ability = oldNode.ability
                this.status = NodeStatus.PREPARE
                this.detail = oldNode.detail?.let { detail ->
                    NodeInstanceDetail {
                        this.input = detail.input
                        this.parameter = detail.parameter
                    }
                }
                this.taskInstances = emptyList()
                this.histories = listOf(
                    NodeInstanceHistory {
                        this.data = oldNode.toString()
                    }
                )
            }
        }

        // 插入新节点
        sqlClient.saveEntities(newNodeInstances) {
            // 追加历史
            setAssociatedMode(NodeInstance::histories, AssociatedSaveMode.APPEND)

            // 清除关联
            setDissociateAction(NodeInstanceDetail::nodeInstance, DissociateAction.DELETE)
            setDissociateAction(TaskInstance::nodeInstance, DissociateAction.DELETE)
            setDissociateAction(TaskInstanceDetail::task, DissociateAction.DELETE)
            setDissociateAction(TaskInstanceResult::task, DissociateAction.DELETE)
        }
    }

    /**
     * 暂停工作流
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun pauseWorkflow(workflowInstanceId: Long) {
        log.info { "暂停工作流: $workflowInstanceId" }

        updateWorkflowStatus(workflowInstanceId, WorkflowStatus.PAUSED)

        eventPublisher.publish(WorkflowWaitingEvent(loadWorkflowInstance(workflowInstanceId).toWork()))
    }

    /**
     * 恢复工作流
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun resumeWorkflow(workflowInstanceId: Long) {
        log.info { "恢复工作流: $workflowInstanceId" }

        val workflowInstance = loadWorkflowInstance(workflowInstanceId)
        if (workflowInstance.status != WorkflowStatus.PAUSED) {
            throw IllegalStateException("只能恢复暂停的工作流")
        }

        updateWorkflowStatus(workflowInstanceId, WorkflowStatus.RUNNING)

        eventPublisher.publish(WorkflowWaitingEvent(loadWorkflowInstance(workflowInstanceId).toWork()))

        // 重新计算就绪节点
        scheduleReadyNodes(workflowInstanceId)
    }

    /**
     * 恢复工作流
     *
     * 重新调度正在运行中的工作流节点
     *
     * @param workflowInstanceId 工作流实例ID
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun recoverWorkflow(workflowInstanceId: Long) {
        log.info { "恢复工作流: $workflowInstanceId" }

        loadNodeInstances(workflowInstanceId)
            .filter { it.status == NodeStatus.RUNNING || it.status == NodeStatus.READY }
            .forEach { node ->
                eventPublisher.publish(NodeReadyEvent(node.toWork()))
            }
    }

    /**
     * 监听节点完成事件
     */
    @EventListener
    fun onNodeCompleted(event: NodeCompletedEvent) = withSystemUser {
        log.info { "${"节点完成: ${event.node.id}"} " }

        // 节点完成后，重新计算就绪节点
        event.node.workflowId?.let {
            scheduleReadyNodes(it)
        }
    }

    /**
     * 监听节点失败事件
     */
    @EventListener
    fun onNodeFailed(event: NodeFailedEvent) = withSystemUser {
        log.info { "节点失败: ${event.node.id}" }

        // 检查工作流是否需要失败
        event.node.workflowId?.let {
            checkWorkflowFailure(it)
        }
    }

    /**
     * 创建工作流实例
     */
    private fun createWorkflowInstance(
        workflowDefinitionId: Long,
        serialNumber: String,
        input: Map<String, Any>,
        parameter: Map<String, Any>,
        nodeArgs: List<NodeDefinitionArgs>,
    ): Long {

        val instance = sqlClient.save(WorkflowInstance {
            workflowDefinition().id = workflowDefinitionId
            this.serialNumber = serialNumber
            mode = WORKFLOW
            status = WorkflowStatus.START
            detail = WorkflowInstanceDetail {
                this.input = input
                this.parameter = parameter
                this.nodeArgs = nodeArgs
            }
        }, SaveMode.INSERT_ONLY, AssociatedSaveMode.APPEND).modifiedEntity

        return instance.id
    }

    /**
     * 加载工作流定义
     */
    private fun loadWorkflowDefinition(workflowDefinitionId: Long): WorkflowDefinition {
        return sqlClient.executeQuery(WorkflowDefinition::class) {
            where(table.id eq workflowDefinitionId)
            select(table.fetchBy {
                allScalarFields()
                nodeDefinitions {
                    allScalarFields()
                    nodeMetadata {
                        allScalarFields()
                    }
                }
                edges {
                    allScalarFields()
                }
            })
        }.firstOrNull() ?: throw IllegalArgumentException("工作流定义不存在: $workflowDefinitionId")
    }

    /**
     * 为工作流创建节点实例
     */
    private fun createNodeInstancesForWorkflow(
        workflowDefinition: WorkflowDefinition,
        workflowInstanceId: Long,
        input: Map<String, Any> = emptyMap(),
        nodeArgs: List<NodeDefinitionArgs> = emptyList()
    ) {
        val nodeArgsMap = nodeArgs.associateBy { it.nodeKey }

        val nodeInstances = workflowDefinition.nodeDefinitions.map { nodeDefinition ->
            val args = nodeArgsMap[nodeDefinition.nodeKey]
            // 兼容前端动态调整节点供应商
            val supplier = args?.supplier ?: nodeDefinition.nodeMetadata.supplier
            val ability = nodeDefinition.nodeMetadata.ability

            NodeInstance {
                nodeDefinition().id = nodeDefinition.id
                workflowInstance().id = workflowInstanceId
                this.supplier = supplier
                this.ability = ability
                status = NodeStatus.PREPARE

                detail {
                    // 如果是 start 节点，转为泛化数据
                    if (nodeDefinition.nodeMetadata.type == NodeType.START) {
                        this.input = input
                    } else if (args !=null) {
                        // 兼容前端动态调整节点参数
                        this.input = args.input ?: mapOf()
                        this.parameter = args.parameter ?: mapOf()
                    }
                }
            }
        }

        sqlClient.saveEntities(nodeInstances, SaveMode.INSERT_ONLY, AssociatedSaveMode.APPEND)
    }

    /**
     * 从数据库构建图状态（实时构建，不缓存）
     */
    private fun buildGraphStateFromDatabase(workflowInstanceId: Long): WorkflowGraphState {
        val workflowInstance = loadWorkflowInstanceWithDefinition(workflowInstanceId)
        val nodeInstances = loadNodeInstances(workflowInstanceId)

        return buildGraphStateFromData(workflowInstance, nodeInstances)
    }

    /**
     * 从工作流实例和节点实例数据构建图状态
     */
    private fun buildGraphStateFromData(
        workflowInstance: WorkflowInstance,
        nodeInstances: List<NodeInstance>
    ): WorkflowGraphState {
        val nodeStatusMap = nodeInstances.associate {
            it.nodeDefinition.nodeKey to it.status
        }
        val nodeInstanceMap = nodeInstances.associate {
            it.nodeDefinition.nodeKey to it.id
        }

        // 构建图节点，使用数据库中的实际状态
        val nodes = workflowInstance.workflowDefinition.nodeDefinitions
            .associate { nodeDefinition ->
                nodeDefinition.nodeKey to GraphNode(
                    nodeDefinition = nodeDefinition,
                    status = nodeStatusMap[nodeDefinition.nodeKey] ?: NodeStatus.PREPARE,
                    dependencies = mutableSetOf(),
                    dependents = mutableSetOf()
                )
            }

        // 构建依赖关系
        workflowInstance.workflowDefinition.edges.forEach { edge ->
            val sourceNode = nodes[edge.sourceNodeKey]
            val targetNode = nodes[edge.targetNodeKey]

            if (sourceNode != null && targetNode != null) {
                targetNode.dependencies.add(edge.sourceNodeKey)
                sourceNode.dependents.add(edge.targetNodeKey)
            }
        }

        return WorkflowGraphState(
            workflowInstanceId = workflowInstance.id,
            nodes = nodes,
            nodeInstances = nodeInstanceMap
        )
    }


    /**
     * 发布工作流启动事件
     */
    private fun publishWorkflowStartEvent(workflowInstanceId: Long) {
        val workflowInstance = loadWorkflowInstance(workflowInstanceId)

        eventPublisher.publish(WorkflowStartEvent(workflowInstance.toWork()))
    }

    /**
     * 计算并调度就绪节点（从数据库实时构建图状态）
     */
    private fun scheduleReadyNodes(workflowInstanceId: Long) {
        // 从数据库实时构建图状态
        val graphState = buildGraphStateFromDatabase(workflowInstanceId)

        // 找出所有入度为0的节点（所有依赖都已完成）
        val readyNodes = graphState.nodes.values.filter { node ->
            node.status == NodeStatus.PREPARE && node.dependencies.all { depKey -> graphState.nodes[depKey]?.status == NodeStatus.COMPLETED }
        }

        checkWorkflowCompletion(workflowInstanceId)

        if (readyNodes.isEmpty()) {
            log.debug { "没有就绪节点，检查工作流完成状态" }
            return
        }

        // 更新节点状态为READY并发布事件
        readyNodes.forEach { node ->
            val nodeInstanceId = graphState.nodeInstances[node.nodeDefinition.nodeKey]
            if (nodeInstanceId != null) {
                // 先更新状态为READY
                updateNodeStatus(nodeInstanceId, NodeStatus.READY)

                // 加载更新后的节点实例
                val nodeInstance = loadNodeInstance(nodeInstanceId)

                // 发布节点就绪事件，触发节点执行
                eventPublisher.publish(NodeReadyEvent(nodeInstance.toWork()))
            }
        }
    }

    /**
     * 更新节点状态（仅更新数据库，不再维护缓存）
     */
    fun updateNodeStatus(nodeInstanceId: Long, status: NodeStatus) {
        // 更新数据库
        sqlClient.save(NodeInstance {
            this.id = nodeInstanceId
            this.status = status
        }, SaveMode.UPDATE_ONLY)
    }


    /**
     * 检查工作流完成状态（从数据库实时构建图状态）
     */
    private fun checkWorkflowCompletion(workflowInstanceId: Long) {
        val graphState = buildGraphStateFromDatabase(workflowInstanceId)

        val allNodes = graphState.nodes.values
        val completedNodes = allNodes.filter { it.status == NodeStatus.COMPLETED }
        val failedNodes = allNodes.filter { it.status == NodeStatus.FAILED }

        when {
            completedNodes.size == allNodes.size -> {
                // 所有节点都完成了
                completeWorkflow(workflowInstanceId)
            }

            failedNodes.isNotEmpty() -> {
                // 有节点失败，检查是否需要终止整个工作流
                val canContinue = checkIfWorkflowCanContinue(graphState)
                if (!canContinue) {
                    failWorkflow(workflowInstanceId)
                }
            }

            else -> {
                // 工作流仍在运行中
                updateWorkflowStatus(workflowInstanceId, WorkflowStatus.RUNNING)
            }
        }
    }

    /**
     * 检查工作流失败状态（从数据库实时构建图状态）
     */
    private fun checkWorkflowFailure(workflowInstanceId: Long) {
        // 一个失败，整体失败，注释部分为
        failWorkflow(workflowInstanceId)
    }

    /**
     * 检查工作流是否可以继续执行
     */
    private fun checkIfWorkflowCanContinue(graphState: WorkflowGraphState): Boolean {
        // 检查是否还有可达的节点
        val completedNodes =
            graphState.nodes.values.filter { it.status == NodeStatus.COMPLETED }.map { it.nodeDefinition.nodeKey }
                .toSet()
        val failedNodes =
            graphState.nodes.values.filter { it.status == NodeStatus.FAILED }.map { it.nodeDefinition.nodeKey }.toSet()

        // 使用DFS查找所有可达节点
        val reachableNodes = mutableSetOf<String>()
        val visited = mutableSetOf<String>()

        fun dfs(nodeKey: String) {
            if (nodeKey in visited) return
            visited.add(nodeKey)

            val node = graphState.nodes[nodeKey] ?: return

            // 如果节点已完成，继续遍历其依赖节点
            if (nodeKey in completedNodes) {
                node.dependents.forEach { dependent ->
                    dfs(dependent)
                }
            }

            // 如果节点的所有依赖都完成了，则这个节点是可达的
            if (node.dependencies.all { it in completedNodes }) {
                reachableNodes.add(nodeKey)
            }
        }

        // 从没有依赖的节点开始DFS
        graphState.nodes.values.filter { it.dependencies.isEmpty() }.forEach { node ->
            dfs(node.nodeDefinition.nodeKey)
        }

        // 如果还有可达的节点且不是失败状态，则可以继续
        return reachableNodes.any { it !in failedNodes && it !in completedNodes }
    }

    /**
     * 完成工作流
     */
    private fun completeWorkflow(workflowInstanceId: Long) {
        log.info { "工作流完成: $workflowInstanceId" }

        updateWorkflowStatus(workflowInstanceId, WorkflowStatus.COMPLETED)

        // 发布工作流完成事件
        val workflowInstance = loadWorkflowInstance(workflowInstanceId)
        eventPublisher.publish(WorkflowCompletedEvent(workflowInstance.toWork()))
    }

    /**
     * 失败工作流
     */
    private fun failWorkflow(workflowInstanceId: Long) {
        log.info { "工作流失败: $workflowInstanceId" }

        updateWorkflowStatus(workflowInstanceId, WorkflowStatus.FAILED)

        // 发布工作流失败事件
        val workflowInstance = loadWorkflowInstance(workflowInstanceId)
        eventPublisher.publish(WorkflowFailedEvent(workflowInstance.toWork()))
    }

    /**
     * 更新工作流状态
     */
    fun updateWorkflowStatus(workflowInstanceId: Long, status: WorkflowStatus) {
        sqlClient.save(WorkflowInstance {
            this.id = workflowInstanceId
            this.status = status
        }, SaveMode.UPDATE_ONLY)
    }

    /**
     * 加载工作流实例
     */
    private fun loadWorkflowInstance(workflowInstanceId: Long): WorkflowInstance {
        return sqlClient.findOneById(newFetcher(WorkflowInstance::class).by {
            allScalarFields()
            detail {
                allScalarFields()
            }
            workflowDefinition {
                allScalarFields()
            }
        }, workflowInstanceId)
    }

    /**
     * 加载工作流实例（包含完整的工作流定义）
     */
    private fun loadWorkflowInstanceWithDefinition(workflowInstanceId: Long): WorkflowInstance {
        return sqlClient.findOneById(newFetcher(WorkflowInstance::class).by {
            allScalarFields()
            workflowDefinition {
                allScalarFields()
                nodeDefinitions {
                    allScalarFields()
                    nodeMetadata {
                        allScalarFields()
                    }
                }
                edges {
                    allScalarFields()
                }
            }
            detail {
                allScalarFields()
            }
        }, workflowInstanceId)
    }

    /**
     * 加载节点实例
     */
    private fun loadNodeInstance(nodeInstanceId: Long): NodeInstance {
        return sqlClient.findOneById(newFetcher(NodeInstance::class).by {
            allScalarFields()
            nodeDefinition {
                allScalarFields()
            }
            workflowInstance {
                allScalarFields()
            }
        }, nodeInstanceId)
    }

    /**
     * 加载工作流的所有节点实例
     */
    private fun loadNodeInstances(workflowInstanceId: Long): List<NodeInstance> {
        return sqlClient.executeQuery(NodeInstance::class) {
            where(table.workflowInstance.id eq workflowInstanceId)
            select(table.fetchBy {
                allScalarFields()
                workflowInstance { }
                nodeDefinition { allScalarFields() }
                detail { allScalarFields() }
            })
        }
    }
}

/**
 * 工作流图状态
 */
data class WorkflowGraphState(
    /**
     * 工作流实例ID
     */
    val workflowInstanceId: Long,
    /**
     * 图节点
     */
    val nodes: Map<String, GraphNode>,
    /**
     * 节点实例ID映射
     */
    val nodeInstances: Map<String, Long>
)

/**
 * 图节点
 */
data class GraphNode(
    /**
     * 节点定义
     */
    val nodeDefinition: NodeDefinition,
    /**
     * 节点状态
     */
    var status: NodeStatus,
    /**
     * 依赖节点
     */
    val dependencies: MutableSet<String>,
    /**
     * 被依赖节点
     */
    val dependents: MutableSet<String>
)
