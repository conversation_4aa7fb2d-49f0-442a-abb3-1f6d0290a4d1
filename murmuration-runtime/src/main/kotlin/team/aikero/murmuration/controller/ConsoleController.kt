package team.aikero.murmuration.controller

import com.fasterxml.jackson.databind.ObjectMapper
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.slf4j.MDC
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.murmuration.core.scheduler.Scheduler
import team.aikero.murmuration.core.workflow.entity.*
import team.aikero.murmuration.core.workflow.event.NodeWork
import team.aikero.murmuration.core.workflow.event.TaskWork
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerRegistry
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.metadata.comment

/**
 * 运维控制台
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@RestController
@RequestMapping("/console")
class ConsoleController(
    val taskScheduler: Scheduler<TaskWork>,
    val nodeScheduler: Scheduler<NodeWork>,
    val sql: KSqlClient,
    val taskHandlerRegistry: TaskHandlerRegistry,
    val objectMapper: ObjectMapper,
) {

    /**
     * 直接触发运行中的任务(插队)
     *
     * @param serialNumber 任务编号
     */
    @PostMapping("/trigger-running-tasks")
    fun triggerRunningTasks(@RequestParam serialNumber: String): DataResponse<Unit> {
        val taskInstances = sql.executeQuery(TaskInstance::class) {
            where(table.nodeInstance.workflowInstance.serialNumber eq serialNumber)
            where(table.status eq TaskStatus.RUNNING)
            select(table.fetchBy {
                nodeInstance()
                supplier()
                ability()
            })
        }

        taskInstances.map(::TaskWork).forEach(taskScheduler::trigger)
        return ok()
    }

    /**
     * 查看工作流队列视图
     *
     * @param serialNumber 任务编号
     */
    @GetMapping("/queue-view")
    fun queueView(@RequestParam serialNumber: String): DataResponse<QueueView> {
        // 工作流实例ID
        val workflowInstanceId = sql.createQuery(WorkflowInstance::class) {
            where(table.serialNumber eq serialNumber)
            select(table.id)
        }.fetchOne()

        val nodes = nodeScheduler.listAll().filter { it.workflowId == workflowInstanceId }
        val tasks = taskScheduler.listAll().filter { it.nodeId in nodes.map { n -> n.id } }

        val view = QueueView(nodes, tasks)
        return ok(view)
    }

    data class QueueView(val nodes: List<NodeWork>, val tasks: List<TaskWork>)

    /**
     * 查看运行中的任务
     *
     * @param serialNumber 任务编号
     */
    @GetMapping("/running-tasks")
    fun runningTasks(@RequestParam serialNumber: String): DataResponse<List<RunningNodeView>> {
        // 获取运行中的节点实例+任务实例
        val runningNodes = sql.executeQuery(NodeInstance::class) {
            where(table.workflowInstance.serialNumber eq serialNumber)
            where(table.status valueIn setOf(NodeStatus.RUNNING, NodeStatus.READY))
            orderBy(table.nodeDefinitionId)
            select(table.fetchBy {
                supplier()
                ability()
                status()
                taskInstances({
                    filter {
                        where(table.status eq TaskStatus.RUNNING)
                    }
                }) {
                    groupIndex()
                    detail {
                        request()
                        context()
                    }
                }
            })
        }

        val runningNodeViews = runningNodes.map { node ->

            // 获取任务处理器
            @Suppress("UNCHECKED_CAST")
            val taskHandler = taskHandlerRegistry.get(node.supplier, node.ability) as TaskHandler<Any, Any>

            RunningNodeView(
                nodeInstanceId = node.id,
                supplier = node.supplier.comment,
                ability = node.ability.comment,
                status = node.status.comment,
                tasks = node.taskInstances.map { task ->

                    // 调用任务处理器
                    val request = objectMapper.treeToValue(task.detail!!.request, taskHandler.requestType.java)
                    val context = objectMapper.treeToValue(task.detail!!.context, taskHandler.contextType.java)
                    val queryResult = MDC.putCloseable("task_id", "${task.id}").use {
                        taskHandler.query(request, context)
                    }

                    RunningTaskView(
                        taskInstanceId = task.id,
                        groupIndex = task.groupIndex,
                        queryResult = queryResult,
                    )
                }
            )
        }

        return ok(runningNodeViews)
    }

    data class RunningNodeView(
        val nodeInstanceId: Long,
        val supplier: String,
        val ability: String,
        val status: String,
        val tasks: List<RunningTaskView>,
    )

    data class RunningTaskView(
        val taskInstanceId: Long,
        val groupIndex: Long,
        val queryResult: TaskResult<List<TaskHandlerResult>>,
    )
}
