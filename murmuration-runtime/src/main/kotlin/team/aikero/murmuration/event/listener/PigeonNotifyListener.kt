package team.aikero.murmuration.event.listener

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.event.ErrorReportEvent
import team.aikero.blade.core.protocol.checkSuccess
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import team.aikero.blade.util.spring.Springs
import team.aikero.murmuration.core.util.requiredProperty
import team.aikero.murmuration.core.workflow.entity.WorkflowInstance
import team.aikero.murmuration.core.workflow.event.NodeReviewingEvent
import team.aikero.murmuration.core.workflow.event.WorkflowCompletedEvent
import team.aikero.murmuration.metadata.comment
import team.aikero.pigeon.common.dto.MessagePayload
import team.aikero.pigeon.sdk.PigeonMessageClient
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

/**
 * 事件通知
 *
 * <AUTHOR>
 */
@Component
class PigeonNotifyListener(
    private var messageClient: PigeonMessageClient,
    private val sqlClient: KSqlClient
){
    /**
     * 监听节点审核事件
     *
     * 当节点进入REVIEWING状态时，推送钉钉验收消息
     */
    @Async
    @EventListener
    fun onNodeReviewing(event: NodeReviewingEvent) = withSystemUser {
        val node = event.node
        val workflowInstanceId = node.workflowId ?: return@withSystemUser

        val instance = sqlClient.findOneById(WorkflowInstance::class, workflowInstanceId)

        try {
            val payload = MessagePayload(
                instance.id.toString(),
                "MURMURATION_POD",
                listOf(instance.creatorId.toString()),
                mutableMapOf(
                    "title" to "[POD]工作流任务待验收",
                    "link" to redirectUrl(instance),
                    "code" to instance.serialNumber,
                    "node" to node.ability.comment,
                )
            )

            messageClient.send(payload).checkSuccess()
        } catch (e: Exception) {
            // 发送失败也没关系，记录一下
            log.error(e) { "发送验收消息失败: ${e.message}" }

            val reportEvent = ErrorReportEvent(
                source = "节点验收",
                ex = e,
                context = mapOf(
                    "工作流实例ID" to workflowInstanceId,
                    "任务编号" to instance.serialNumber,
                    "节点实例ID" to node.id,
                    "发起人" to instance.creatorName
                ),
            )
            Springs.publishEvent(reportEvent)
        }
    }

    /**
     * 工作流完成时发送消息
     */
    @Async
    @EventListener
    fun onWorkflowCompleted(event: WorkflowCompletedEvent) = withSystemUser {
        val instance = sqlClient.findOneById(WorkflowInstance::class, event.workflow.id)

        try {
            val payload = MessagePayload(
                instance.id.toString(),
                "MURMURATION_WORKFLOW_SUCCESS",
                listOf(instance.creatorId.toString()),
                mutableMapOf(
                    "title" to "[POD]工作流任务已完成",
                    "link" to redirectUrl(instance),
                    "code" to instance.serialNumber,
                )
            )

            messageClient.send(payload).checkSuccess()
        } catch (e: Exception) {
            // 发送失败也没关系，记录一下
            log.error(e) { "发送验收消息失败: ${e.message}" }

            val reportEvent = ErrorReportEvent(
                source = "节点验收",
                ex = e,
                context = mapOf(
                    "工作流实例ID" to event.workflow.id,
                    "任务编号" to instance.serialNumber,
                    "发起人" to instance.creatorName
                ),
            )
            Springs.publishEvent(reportEvent)
        }

    }

    private fun redirectUrl(instance: WorkflowInstance): String {
        //{"serialNumber":"任务编号"}
        val param = URLEncoder.encode(mapOf("serialNumber" to instance.serialNumber).toJson(), StandardCharsets.UTF_8)
        val url = requiredProperty<String>("dingtalk.pod-url")
        return url+ param
    }
}
